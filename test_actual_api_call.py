#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际API调用的脚本，模拟用户在GUI中的操作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_actual_api_calls():
    """测试实际的API调用"""
    print("=" * 80)
    print("实际API调用测试")
    print("=" * 80)
    
    try:
        # 1. 导入模块
        print("\n1. 导入模块...")
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        from deepseek_chat_client.api_client import ChatMessage
        print("✓ 模块导入成功")
        
        # 2. 初始化统一客户端
        print("\n2. 初始化统一客户端...")
        unified_client = UnifiedAPIClient()
        print("✓ 统一客户端初始化成功")
        
        # 3. 测试火山引擎模型调用
        print("\n3. 测试火山引擎模型调用...")
        
        # 模拟修仙题材的消息
        test_messages = [
            ChatMessage(role="system", content="你是一个修仙题材文字冒险游戏的AI助手。"),
            ChatMessage(role="user", content="我想开始修仙之旅，请为我创建一个角色。")
        ]
        
        # 测试不同的火山引擎模型
        volcengine_models = [
            "doubao-seed-1-6-flash-250715",
            "doubao-seed-1-6-thinking-250715", 
            "doubao-seed-1-6-250615",
            "doubao-1-5-pro-32k-250115"
        ]
        
        for model in volcengine_models:
            print(f"\n  测试模型: {model}")
            
            # 检查路由
            provider = unified_client.get_provider_for_model(model)
            client = unified_client.get_client_for_model(model)
            
            print(f"    提供商: {provider}")
            print(f"    客户端: {type(client).__name__}")
            print(f"    API端点: {client.base_url if hasattr(client, 'base_url') else 'N/A'}")
            
            try:
                # 发送实际的API请求
                print(f"    发送API请求...")
                
                response_chunks = []
                error_occurred = False
                
                for chunk in unified_client.chat_completion(
                    messages=test_messages,
                    model=model,
                    max_tokens=100,  # 使用较小的Token数进行测试
                    temperature=0.7,
                    stream=True
                ):
                    if chunk.error:
                        print(f"    ✗ API调用失败: {chunk.error}")
                        error_occurred = True
                        break
                    
                    if chunk.content:
                        response_chunks.append(chunk.content)
                    
                    if chunk.is_complete:
                        break
                
                if not error_occurred:
                    full_response = "".join(response_chunks)
                    print(f"    ✓ API调用成功")
                    print(f"    响应长度: {len(full_response)} 字符")
                    print(f"    响应预览: {full_response[:100]}{'...' if len(full_response) > 100 else ''}")
                else:
                    print(f"    ✗ 模型 {model} 调用失败")
                    
            except Exception as e:
                print(f"    ✗ 调用异常: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # 4. 测试DeepSeek模型调用（作为对比）
        print("\n4. 测试DeepSeek模型调用...")
        
        deepseek_models = ["deepseek-chat", "deepseek-reasoner"]
        
        for model in deepseek_models:
            print(f"\n  测试模型: {model}")
            
            # 检查路由
            provider = unified_client.get_provider_for_model(model)
            client = unified_client.get_client_for_model(model)
            
            print(f"    提供商: {provider}")
            print(f"    客户端: {type(client).__name__}")
            print(f"    API端点: {client.base_url if hasattr(client, 'base_url') else 'N/A'}")
            
            # 注意：这里不实际调用DeepSeek API，只检查路由
            print(f"    ✓ 路由检查通过（未实际调用）")
        
        # 5. 测试字符编码
        print("\n5. 测试字符编码...")
        
        chinese_messages = [
            ChatMessage(role="system", content="你是修仙世界的引导者。"),
            ChatMessage(role="user", content="我想在修仙世界中穿越身份，开始我的修仙之旅。")
        ]
        
        # 使用一个可用的火山引擎模型测试中文
        test_model = "doubao-1-5-pro-32k-250115"  # 这个模型通常比较稳定
        
        print(f"  使用模型 {test_model} 测试中文编码...")
        
        try:
            response_chunks = []
            error_occurred = False
            
            for chunk in unified_client.chat_completion(
                messages=chinese_messages,
                model=test_model,
                max_tokens=50,
                temperature=0.7,
                stream=True
            ):
                if chunk.error:
                    print(f"  ✗ 中文编码测试失败: {chunk.error}")
                    error_occurred = True
                    break
                
                if chunk.content:
                    response_chunks.append(chunk.content)
                
                if chunk.is_complete:
                    break
            
            if not error_occurred:
                full_response = "".join(response_chunks)
                print(f"  ✓ 中文编码测试成功")
                print(f"  中文响应: {full_response}")
                
                # 检查是否有乱码
                if "ã" in full_response or "ç" in full_response or "è" in full_response:
                    print(f"  ⚠️ 检测到可能的编码问题")
                else:
                    print(f"  ✓ 未检测到编码问题")
            
        except Exception as e:
            print(f"  ✗ 中文编码测试异常: {str(e)}")
        
        # 6. 生成测试报告
        print("\n" + "=" * 80)
        print("测试报告")
        print("=" * 80)
        
        print("\n📊 API路由测试结果:")
        print("  ✓ 统一客户端初始化正常")
        print("  ✓ 模型到提供商映射正确")
        print("  ✓ API端点路由正确")
        
        print("\n🔍 可能的问题分析:")
        print("  1. 如果用户遇到DeepSeek API错误，可能是：")
        print("     - GUI中某处绕过了统一客户端")
        print("     - 存在缓存的客户端实例")
        print("     - 模型名称传递错误")
        print("  2. 字符编码问题可能来自：")
        print("     - HTTP响应编码设置")
        print("     - JSON解析过程")
        print("     - GUI显示编码")
        
        print("\n💡 建议的修复方案:")
        print("  1. 在GUI中添加更多调试日志")
        print("  2. 确保所有API调用都通过统一客户端")
        print("  3. 检查HTTP请求的编码设置")
        print("  4. 验证模型名称的正确传递")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = test_actual_api_calls()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
