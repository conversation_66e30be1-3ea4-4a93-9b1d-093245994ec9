#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Function Calling系统测试用例
测试函数注册、执行、GUI集成等功能
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_function_registry():
    """测试函数注册表功能"""
    print("=" * 60)
    print("测试函数注册表功能")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.function_calling import function_registry, register_function
        
        # 测试注册简单函数
        @register_function(
            name="test_add",
            description="测试加法函数",
            category="test",
            tags=["math", "test"]
        )
        def test_add(a: int, b: int) -> int:
            """计算两个数的和"""
            return a + b
        
        # 测试注册带默认参数的函数
        @register_function(
            name="test_greet",
            description="测试问候函数",
            category="test"
        )
        def test_greet(name: str, greeting: str = "Hello") -> str:
            """生成问候语"""
            return f"{greeting}, {name}!"
        
        # 测试获取函数列表
        functions = function_registry.list_functions(category="test")
        print(f"✓ 注册的测试函数: {functions}")
        
        # 测试获取函数Schema
        schemas = function_registry.get_functions_schema(category="test")
        print(f"✓ 生成的Schema数量: {len(schemas)}")
        
        # 测试函数调用
        add_func = function_registry.get_function_callable("test_add")
        if add_func:
            result = add_func(3, 5)
            print(f"✓ test_add(3, 5) = {result}")
        
        greet_func = function_registry.get_function_callable("test_greet")
        if greet_func:
            result = greet_func("World")
            print(f"✓ test_greet('World') = {result}")
        
        # 测试统计信息
        stats = function_registry.get_stats()
        print(f"✓ 注册表统计: {stats}")
        
        print("✓ 函数注册表测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 函数注册表测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_function_executor():
    """测试函数执行器功能"""
    print("\n" + "=" * 60)
    print("测试函数执行器功能")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.function_calling import FunctionExecutor, function_registry
        from deepseek_chat_client.function_calling.executor import FunctionCallRequest
        
        # 创建执行器
        executor = FunctionExecutor(timeout=5.0, max_concurrent=2)
        
        # 测试函数调用请求
        request = FunctionCallRequest(
            name="test_add",
            arguments={"a": 10, "b": 20},
            call_id="test_001"
        )
        
        result = executor.execute_function_call(request)
        print(f"✓ 函数执行结果: success={result.success}, result={result.result}, time={result.execution_time:.3f}s")
        
        # 测试不存在的函数
        invalid_request = FunctionCallRequest(
            name="non_existent_function",
            arguments={},
            call_id="test_002"
        )
        
        result = executor.execute_function_call(invalid_request)
        print(f"✓ 无效函数测试: success={result.success}, error={result.error}")
        
        # 测试参数验证
        invalid_args_request = FunctionCallRequest(
            name="test_add",
            arguments={"a": "not_a_number", "b": 20},
            call_id="test_003"
        )
        
        result = executor.execute_function_call(invalid_args_request)
        print(f"✓ 参数验证测试: success={result.success}")
        
        print("✓ 函数执行器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 函数执行器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_builtin_functions():
    """测试内置函数"""
    print("\n" + "=" * 60)
    print("测试内置函数")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.function_calling import function_registry, FunctionExecutor
        from deepseek_chat_client.function_calling.executor import FunctionCallRequest
        
        executor = FunctionExecutor()
        
        # 测试时间函数
        time_request = FunctionCallRequest(
            name="get_current_time",
            arguments={},
            call_id="builtin_001"
        )
        
        result = executor.execute_function_call(time_request)
        print(f"✓ get_current_time(): {result.result}")
        
        # 测试计算函数
        calc_request = FunctionCallRequest(
            name="calculate",
            arguments={"expression": "2 + 3 * 4"},
            call_id="builtin_002"
        )
        
        result = executor.execute_function_call(calc_request)
        print(f"✓ calculate('2 + 3 * 4'): {result.result}")
        
        # 测试系统信息函数
        sys_request = FunctionCallRequest(
            name="get_system_info",
            arguments={},
            call_id="builtin_003"
        )
        
        result = executor.execute_function_call(sys_request)
        if result.success:
            sys_info = json.loads(result.result)
            print(f"✓ get_system_info(): 获取到 {len(sys_info)} 项系统信息")
        
        print("✓ 内置函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 内置函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_schema_generation():
    """测试Schema生成"""
    print("\n" + "=" * 60)
    print("测试Schema生成")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.function_calling import SchemaGenerator
        from typing import List, Optional
        
        generator = SchemaGenerator()
        
        # 测试复杂函数的Schema生成
        def complex_function(
            name: str,
            age: int,
            tags: List[str],
            description: Optional[str] = None
        ) -> dict:
            """
            复杂函数示例
            
            Args:
                name: 用户名称
                age: 用户年龄
                tags: 标签列表
                description: 可选描述
                
            Returns:
                用户信息字典
            """
            return {"name": name, "age": age, "tags": tags, "description": description}
        
        schema = generator.generate_schema(complex_function, "complex_test", "测试复杂Schema生成")
        print(f"✓ 生成的Schema: {json.dumps(schema, indent=2, ensure_ascii=False)}")
        
        # 验证Schema结构
        assert schema["type"] == "function"
        assert "function" in schema
        assert "name" in schema["function"]
        assert "parameters" in schema["function"]
        assert "properties" in schema["function"]["parameters"]
        assert "required" in schema["function"]["parameters"]
        
        print("✓ Schema生成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ Schema生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_management():
    """测试配置管理"""
    print("\n" + "=" * 60)
    print("测试配置管理")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.function_calling import function_calling_config
        
        # 测试配置读取
        print(f"✓ Function Calling启用状态: {function_calling_config.is_enabled()}")
        print(f"✓ 超时时间: {function_calling_config.get_timeout()}秒")
        print(f"✓ 最大并发数: {function_calling_config.get_max_concurrent_calls()}")
        print(f"✓ 内置函数启用: {function_calling_config.is_builtin_functions_enabled()}")
        print(f"✓ 调试模式: {function_calling_config.is_debug_enabled()}")
        
        # 测试配置修改
        original_timeout = function_calling_config.get_timeout()
        function_calling_config.set_timeout(15.0, save=False)
        new_timeout = function_calling_config.get_timeout()
        print(f"✓ 配置修改测试: {original_timeout} -> {new_timeout}")
        
        # 恢复原始配置
        function_calling_config.set_timeout(original_timeout, save=False)
        
        print("✓ 配置管理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_integration():
    """测试API集成"""
    print("\n" + "=" * 60)
    print("测试API集成")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        from deepseek_chat_client.api_client import ChatMessage
        
        # 创建统一客户端
        client = UnifiedAPIClient()
        
        # 测试Function Calling状态
        print(f"✓ Function Calling启用状态: {client.is_function_calling_enabled()}")
        
        # 测试获取可用函数
        functions = client.get_available_functions()
        print(f"✓ 可用函数数量: {len(functions) if functions else 0}")
        
        # 测试统计信息
        stats = client.get_function_calling_stats()
        print(f"✓ Function Calling统计: {stats}")
        
        print("✓ API集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ API集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("开始Function Calling系统测试")
    print("=" * 80)
    
    tests = [
        test_function_registry,
        test_function_executor,
        test_builtin_functions,
        test_schema_generation,
        test_config_management,
        test_api_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 异常: {e}")
            failed += 1
    
    print("\n" + "=" * 80)
    print(f"测试完成: {passed} 通过, {failed} 失败")
    print("=" * 80)
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
