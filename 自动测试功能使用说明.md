# 文本游戏自动测试功能使用说明

## 功能概述

本自动测试功能为文本游戏提供了完全自动化的测试能力，包括：

1. **自动选择功能**：当游戏出现选择项时，系统能够自动随机选择其中一个选项
2. **自动执行回合控制**：连续进行多个回合而不需要用户手动确认
3. **回合数量设定**：允许用户设定要自动执行的回合数量（1-1000回合）
4. **详细日志记录**：记录自动测试的执行过程，便于跟踪和调试

## 界面说明

### 自动测试控制面板

在主界面的工具栏右侧，您会看到一个"自动测试"控制面板，包含以下元素：

#### 第一行：自动选择控制
- **自动选择复选框**：启用/禁用自动选择功能
- **延迟设置**：设定自动选择的延迟时间（1-10秒）

#### 第二行：自动执行控制
- **自动执行复选框**：启用/禁用自动执行功能
- **回合数设置**：设定要自动执行的回合数量（1-1000回合）

#### 第三行：控制按钮和状态
- **开始/停止按钮**：控制自动测试的开始和停止
- **状态显示**：显示当前自动测试的状态

## 使用方法

### 基本使用流程

1. **创建游戏会话**
   - 点击"新建会话"按钮
   - 点击"开始游戏"按钮启动游戏

2. **配置自动测试**
   - 勾选"自动选择"复选框
   - 勾选"自动执行"复选框
   - 设定所需的回合数量（默认10回合）
   - 调整自动选择延迟（默认3秒）

3. **开始自动测试**
   - 点击"开始自动测试"按钮
   - 系统将自动执行指定数量的回合

4. **监控测试过程**
   - 观察游戏消息区域的自动测试日志
   - 查看状态显示的当前进度

5. **停止测试**
   - 点击"停止自动测试"按钮（可随时停止）
   - 或等待自动完成所有回合

### 使用模式

#### 模式一：仅自动选择
- 只勾选"自动选择"
- 当游戏出现选择项时，系统会自动随机选择
- 需要手动点击继续下一回合

#### 模式二：完全自动测试
- 同时勾选"自动选择"和"自动执行"
- 系统会连续自动执行指定数量的回合
- 无需任何手动干预

## 功能特性

### 智能选择机制
- 随机从可用的快捷回复选项中选择
- 支持各种类型的游戏选择（对话选项、行动选择等）
- 自动处理选项文本的显示和截断

### 安全机制
- 自动检测游戏结束标记，防止无限循环
- 异常情况自动停止测试
- 支持随时手动停止测试

### 详细日志记录
- **控制台日志**：实时显示在游戏消息区域
- **文件日志**：保存到 `logs/auto_test/` 目录
- **日志级别**：INFO、DEBUG、ERROR、WARNING、SUMMARY
- **测试总结**：包含开始时间、结束时间、完成率等统计信息

### 状态管理
- 实时显示当前回合进度
- 显示测试状态（未启用、就绪、运行中等）
- 自动更新UI控件状态

## 日志文件

### 日志位置
自动测试日志保存在：`logs/auto_test/auto_test_YYYYMMDD_HHMMSS.log`

### 日志内容
- 测试开始和结束时间
- 每个回合的执行记录
- 选择的选项内容
- 错误和异常信息
- 测试完成总结

### 日志示例
```
自动测试日志 - 2025-08-02 06:17:51
==================================================

2025-08-02 06:17:51 [INFO] 开始自动测试，目标回合数: 10
2025-08-02 06:17:54 [DEBUG] 执行第 1 回合
2025-08-02 06:17:54 [INFO] 随机选择选项: 继续探索
2025-08-02 06:18:02 [DEBUG] 执行第 2 回合
...
2025-08-02 06:20:15 [INFO] 自动测试完成，总回合数: 10

==================================================
自动测试完成总结:
- 开始时间: 2025-08-02 06:17:51
- 结束时间: 2025-08-02 06:20:15
- 持续时间: 0:02:24
- 完成回合数: 10
- 目标回合数: 10
- 完成率: 100.0%
==================================================
```

## 注意事项

### 使用前提
1. 必须先创建游戏会话
2. 必须启动游戏（有快捷回复选项出现）
3. 同时启用"自动选择"和"自动执行"才能进行完全自动测试

### 性能考虑
- 建议设置合理的延迟时间（3-5秒）
- 大量回合测试时注意系统资源使用
- 可随时停止测试以避免过度消耗

### 兼容性
- 支持所有类型的文本游戏
- 兼容快捷回复和Function Calling功能
- 适用于各种游戏场景（RPG、冒险、对话等）

## 故障排除

### 常见问题

1. **按钮显示为禁用状态**
   - 确保同时启用了"自动选择"和"自动执行"
   - 检查是否已创建游戏会话

2. **自动测试无法开始**
   - 确认游戏已启动且有快捷回复选项
   - 检查是否有活动的游戏会话

3. **测试中途停止**
   - 检查是否遇到游戏结束标记
   - 查看日志文件了解具体错误信息

4. **日志文件未生成**
   - 确保有写入权限
   - 检查 `logs/auto_test/` 目录是否存在

### 调试建议
- 查看控制台输出的详细调试信息
- 检查自动测试日志文件
- 使用较小的回合数进行初始测试
- 逐步增加延迟时间以观察效果

## 更新历史

- **v1.0** (2025-08-02)
  - 实现基本的自动选择功能
  - 添加自动回合执行控制
  - 集成详细日志记录系统
  - 提供完整的用户界面控制
