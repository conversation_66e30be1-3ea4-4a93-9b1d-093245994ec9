#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主线剧情衔接功能
专门调试对话结束后的主线剧情衔接问题
"""

import sys
import tkinter as tk
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI


def test_main_story_continuation():
    """测试主线剧情衔接功能"""
    print("=" * 80)
    print("测试主线剧情衔接功能")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        print("1. 测试触发对话并观察衔接过程...")
        
        # 使用简短的对话进行测试（1回合）
        test_response = '''
〖测试章节〗第1回〖测试场景〗这是一个测试场景。

【测试NPC】（测试身份）
测试对话内容。

```json
{"触发关键聊天剧情":["测试NPC", "测试身份","你是测试NPC，进行简短的测试对话。","测试背景信息。","测试性格描述。","测试对话情境。","1回合"]}
```

```json
{"quick_replies":["测试选项1", "测试选项2"]}
```
        '''
        
        print("2. 模拟接收AI响应...")
        # 模拟接收到AI响应
        gui._display_message("assistant", test_response)
        gui.history_manager.add_message("assistant", test_response)
        
        print("3. 触发对话检测...")
        # 解析并显示（这会触发对话窗口）
        result = gui._check_dialogue_trigger(test_response)
        
        if result:
            print("✓ 对话触发成功")
            print("\n请观察以下调试信息：")
            print("- 对话窗口是否正确创建")
            print("- NPC是否自动开场")
            print("- 进行1轮对话后是否自动结束")
            print("- 对话结束后是否调用回调函数")
            print("- 是否触发主线剧情衔接")
            print("- 主聊天窗口是否显示衔接提示和AI响应")
            
            print("\n" + "=" * 60)
            print("调试检查清单：")
            print("=" * 60)
            print("□ [DEBUG] 对话完成回调被调用")
            print("□ [DEBUG] _trigger_main_story_continuation 被调用")
            print("□ [DEBUG] _send_continuation_api_request 开始执行")
            print("□ [DEBUG] 在主窗口显示衔接提示...")
            print("□ [DEBUG] 开始发送API请求...")
            print("□ [DEBUG] API响应接收完成")
            print("□ [DEBUG] 主线剧情衔接完成")
            
            print("\n如果以上调试信息没有完整出现，说明存在问题。")
            
        else:
            print("✗ 对话触发失败")
            return False
        
        # 等待一段时间让用户观察
        print("\n等待10秒让用户观察调试信息...")
        time.sleep(10)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def test_callback_chain():
    """测试回调链路"""
    print("=" * 80)
    print("测试回调链路")
    print("=" * 80)
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        print("1. 测试回调函数存在性...")
        
        # 检查关键方法是否存在
        methods_to_check = [
            '_on_dialogue_complete',
            '_trigger_main_story_continuation', 
            '_send_continuation_api_request',
            '_finish_sending'
        ]
        
        for method_name in methods_to_check:
            if hasattr(gui, method_name):
                print(f"  ✓ {method_name} 方法存在")
            else:
                print(f"  ✗ {method_name} 方法缺失")
        
        print("\n2. 测试直接调用回调函数...")
        
        # 直接测试回调函数
        test_history = "=== 测试对话历史 ==="
        test_npc_name = "测试NPC"
        
        print("调用 _on_dialogue_complete...")
        try:
            gui._on_dialogue_complete(test_history, test_npc_name)
            print("✓ _on_dialogue_complete 调用成功")
        except Exception as e:
            print(f"✗ _on_dialogue_complete 调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n3. 测试直接调用衔接函数...")
        
        print("调用 _trigger_main_story_continuation...")
        try:
            gui._trigger_main_story_continuation(test_npc_name)
            print("✓ _trigger_main_story_continuation 调用成功")
        except Exception as e:
            print(f"✗ _trigger_main_story_continuation 调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 等待一段时间观察结果
        print("\n等待5秒观察API调用结果...")
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"✗ 回调链路测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def main():
    """主测试函数"""
    print("🔧 开始调试主线剧情衔接功能")
    print("=" * 100)
    
    print("📋 调试目标：")
    print("1. 检查对话完成回调是否正确调用")
    print("2. 检查主线剧情衔接是否正确触发")
    print("3. 检查API请求是否正确发送")
    print("4. 检查主聊天窗口是否正确显示结果")
    print()
    
    test_results = []
    
    # 运行测试
    print("🧪 开始测试...")
    test_results.append(("回调链路测试", test_callback_chain()))
    test_results.append(("完整流程测试", test_main_story_continuation()))
    
    # 汇总结果
    print("\n" + "=" * 100)
    print("📊 调试结果汇总")
    print("=" * 100)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 调试完成！请查看控制台输出的详细调试信息。")
        print("\n💡 如果主线剧情衔接仍然不工作，请检查：")
        print("1. 对话窗口是否正确调用了回调函数")
        print("2. API客户端是否正常工作")
        print("3. 网络连接是否正常")
        print("4. 是否存在线程同步问题")
    else:
        print("\n❌ 发现问题，请根据错误信息进行修复")
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
