#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DeepSeek-Reasoner模型思考内容显示修复效果
"""

import sys
import tkinter as tk
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI


def create_thinking_test_gui():
    """创建思考内容测试GUI"""
    print("创建思考内容显示测试GUI...")
    
    # 创建主窗口
    app = ChatGUI()
    
    # 使用DeepSeek-Reasoner模型进行测试
    app.current_model.set("deepseek-reasoner")
    
    # 创建测试响应，包含触发关键聊天剧情的内容
    test_response = '''
〖思考内容测试〗第1回〖测试场景〗这是一个专门用于测试DeepSeek-Reasoner模型思考内容显示的场景。

【测试NPC】（测试身份/测试修为）
测试NPC看着你说道："这是一个测试对话，用于验证思考内容是否能正确显示。"

```json
{"触发关键聊天剧情":["测试NPC", "测试身份/测试修为","你是测试NPC，专门用于测试思考内容显示功能。请在回复中包含思考过程，以验证思考内容能否正确解析和显示。","你与玩家是测试关系，用于验证功能。","你性格直接，专门用于测试思考内容显示。","当前在测试场景中，需要验证思考内容的显示效果。","3回合"]}
```

```json
{"quick_replies":["测试思考内容", "验证显示效果", "继续测试"]}
```
    '''
    
    # 添加测试控制面板
    test_frame = tk.Frame(app.main_frame)
    test_frame.pack(fill=tk.X, pady=10)
    
    # 标题
    title_label = tk.Label(
        test_frame,
        text="🧠 DeepSeek-Reasoner思考内容显示测试",
        font=("Microsoft YaHei", 14, "bold"),
        fg="#e74c3c"
    )
    title_label.pack(pady=5)
    
    # 说明文字
    info_text = """
🎯 测试目标：验证DeepSeek-Reasoner模型思考内容的正确显示

📋 测试场景：
1. 主线剧情衔接：对话结束后的主线剧情衔接中的思考内容显示
2. 对话窗口：触发关键聊天剧情创建的对话窗口中的思考内容显示
3. 普通聊天：主聊天窗口中的思考内容显示（对照组）

🔍 验证要点：
- 思考内容应以灰色斜体文字显示
- 思考内容应在正文内容之前显示
- 思考内容不应保存到历史记录中
- 所有场景下的思考内容都应正确显示

⚠️ 注意事项：
- 请确保当前模型设置为 deepseek-reasoner
- 观察思考内容的颜色和字体样式
- 检查思考内容是否完整显示
    """
    
    info_label = tk.Label(
        test_frame,
        text=info_text,
        font=("Microsoft YaHei", 9),
        fg="#2c3e50",
        justify=tk.LEFT
    )
    info_label.pack(pady=5)
    
    # 测试状态显示
    status_frame = tk.Frame(test_frame)
    status_frame.pack(fill=tk.X, pady=5)
    
    status_var = tk.StringVar(value="等待开始测试...")
    status_label = tk.Label(
        status_frame,
        textvariable=status_var,
        font=("Microsoft YaHei", 10, "bold"),
        fg="#27ae60"
    )
    status_label.pack()
    
    # 测试步骤跟踪
    steps_frame = tk.Frame(test_frame)
    steps_frame.pack(fill=tk.X, pady=5)
    
    steps_text = tk.Text(
        steps_frame,
        height=10,
        width=80,
        font=("Consolas", 9),
        bg="#f8f9fa",
        fg="#2c3e50"
    )
    steps_text.pack(fill=tk.BOTH, expand=True)
    
    def log_step(message):
        """记录测试步骤"""
        timestamp = time.strftime("%H:%M:%S")
        steps_text.insert(tk.END, f"[{timestamp}] {message}\n")
        steps_text.see(tk.END)
        steps_text.update()
    
    def start_thinking_test():
        """开始思考内容测试"""
        log_step("🧠 开始DeepSeek-Reasoner思考内容显示测试")
        status_var.set("测试进行中...")
        
        # 确保使用正确的模型
        current_model = app.current_model.get()
        if current_model != "deepseek-reasoner":
            log_step(f"⚠️  当前模型: {current_model}，建议切换到 deepseek-reasoner")
        else:
            log_step("✅ 当前模型: deepseek-reasoner")
        
        # 清空之前的消息
        app.message_text.config(state=tk.NORMAL)
        app.message_text.delete(1.0, tk.END)
        app.message_text.config(state=tk.DISABLED)
        
        log_step("📤 模拟接收AI响应（包含触发关键聊天剧情）...")
        # 模拟接收到AI响应
        app._display_message("assistant", test_response)
        app.history_manager.add_message("assistant", test_response)
        
        log_step("🔍 触发对话检测...")
        # 解析并显示（这会触发对话窗口）
        result = app._check_dialogue_trigger(test_response)
        
        if result:
            log_step("✅ 对话触发成功，对话窗口已创建")
            log_step("👀 请观察以下内容：")
            log_step("   1. 对话窗口中NPC自动开场时的思考内容显示")
            log_step("   2. 与NPC对话时的思考内容显示")
            log_step("   3. 对话结束后主线剧情衔接时的思考内容显示")
            log_step("   4. 思考内容应为灰色斜体文字")
            status_var.set("对话窗口已创建，请观察思考内容显示...")
        else:
            log_step("❌ 对话触发失败")
            status_var.set("测试失败：对话触发失败")
    
    def test_main_chat_thinking():
        """测试主聊天窗口的思考内容显示"""
        log_step("🧠 测试主聊天窗口思考内容显示")
        status_var.set("测试主聊天窗口...")
        
        # 发送一个简单的测试消息
        test_message = "请用DeepSeek-Reasoner模型回复这条消息，并在回复中包含思考过程。"
        
        # 模拟用户输入
        app.input_text.delete(1.0, tk.END)
        app.input_text.insert(1.0, test_message)
        
        log_step(f"📤 发送测试消息: {test_message}")
        log_step("👀 请观察主聊天窗口中的思考内容显示效果")
        
        # 触发发送
        app._send_message()
        
        status_var.set("主聊天测试进行中，请观察思考内容...")
    
    def reset_test():
        """重置测试"""
        log_step("🔄 重置测试环境")
        status_var.set("等待开始测试...")
        steps_text.delete(1.0, tk.END)
        
        # 清空聊天记录
        app.message_text.config(state=tk.NORMAL)
        app.message_text.delete(1.0, tk.END)
        app.message_text.config(state=tk.DISABLED)
    
    # 测试按钮
    button_frame = tk.Frame(test_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    start_button = tk.Button(
        button_frame,
        text="🚀 开始对话窗口测试",
        command=start_thinking_test,
        bg="#3498db",
        fg="white",
        font=("Microsoft YaHei", 11, "bold"),
        padx=20,
        pady=5
    )
    start_button.pack(side=tk.LEFT, padx=5)
    
    main_chat_button = tk.Button(
        button_frame,
        text="🧠 测试主聊天思考内容",
        command=test_main_chat_thinking,
        bg="#9b59b6",
        fg="white",
        font=("Microsoft YaHei", 11, "bold"),
        padx=20,
        pady=5
    )
    main_chat_button.pack(side=tk.LEFT, padx=5)
    
    reset_button = tk.Button(
        button_frame,
        text="🔄 重置测试",
        command=reset_test,
        bg="#95a5a6",
        fg="white",
        font=("Microsoft YaHei", 11, "bold"),
        padx=20,
        pady=5
    )
    reset_button.pack(side=tk.LEFT, padx=5)
    
    print("思考内容测试GUI创建完成")
    print("=" * 80)
    print("🧠 DeepSeek-Reasoner思考内容显示测试")
    print("=" * 80)
    print("本测试将验证以下修复效果：")
    print("1. 主线剧情衔接时的思考内容显示")
    print("2. 对话窗口中的思考内容显示")
    print("3. 主聊天窗口中的思考内容显示（对照组）")
    print()
    print("请在GUI中点击相应按钮开始测试！")
    print("=" * 80)
    
    return app


def main():
    """主函数"""
    print("🧠 启动DeepSeek-Reasoner思考内容显示测试程序")
    print("=" * 100)
    
    try:
        # 创建测试GUI
        app = create_thinking_test_gui()
        
        # 运行GUI主循环
        app.run()
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
        return 0
    except Exception as e:
        print(f"💥 测试程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("测试完成")
    return 0


if __name__ == "__main__":
    sys.exit(main())
