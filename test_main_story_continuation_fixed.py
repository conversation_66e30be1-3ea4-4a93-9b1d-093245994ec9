#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的主线剧情衔接功能测试
使用正确的GUI主循环来避免线程同步问题
"""

import sys
import tkinter as tk
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI


def create_test_gui():
    """创建测试GUI"""
    print("创建主线剧情衔接测试GUI...")
    
    # 创建主窗口
    app = ChatGUI()
    
    # 使用简短的对话进行测试（2回合）
    test_response = '''
〖测试章节〗第1回〖测试场景〗这是一个测试场景，用于验证主线剧情衔接功能。

【测试NPC】（测试身份/测试修为）
测试NPC看着你说道："这是一个测试对话，我们来验证主线剧情衔接功能是否正常工作。"

```json
{"触发关键聊天剧情":["测试NPC", "测试身份/测试修为","你是测试NPC，用于测试主线剧情衔接功能。请进行简短的对话测试。","你与玩家是测试关系。","你性格直接，专门用于测试。","当前在测试场景中，需要验证对话结束后的主线剧情衔接。","2回合"]}
```

```json
{"quick_replies":["开始测试", "验证功能", "继续对话"]}
```
    '''
    
    # 添加测试控制面板
    test_frame = tk.Frame(app.main_frame)
    test_frame.pack(fill=tk.X, pady=10)
    
    # 标题
    title_label = tk.Label(
        test_frame,
        text="🔧 主线剧情衔接功能测试",
        font=("Microsoft YaHei", 14, "bold"),
        fg="#e74c3c"
    )
    title_label.pack(pady=5)
    
    # 说明文字
    info_text = """
🎯 测试目标：验证对话结束后的主线剧情衔接功能

📋 测试步骤：
1. 点击"开始测试"按钮触发对话
2. 观察对话窗口是否正确创建和自动开场
3. 与NPC进行2轮对话（会自动结束）
4. 观察对话结束后是否自动触发主线剧情衔接
5. 检查主聊天窗口是否显示衔接提示和AI响应

🔍 关键调试信息：
- [DEBUG] 对话完成回调被调用
- [DEBUG] _trigger_main_story_continuation 被调用
- [DEBUG] _send_continuation_api_request 开始执行
- [DEBUG] 在主窗口显示衔接提示...
- [DEBUG] 开始发送API请求...
- [DEBUG] 主线剧情衔接完成
    """
    
    info_label = tk.Label(
        test_frame,
        text=info_text,
        font=("Microsoft YaHei", 9),
        fg="#2c3e50",
        justify=tk.LEFT
    )
    info_label.pack(pady=5)
    
    # 测试状态显示
    status_frame = tk.Frame(test_frame)
    status_frame.pack(fill=tk.X, pady=5)
    
    status_var = tk.StringVar(value="等待开始测试...")
    status_label = tk.Label(
        status_frame,
        textvariable=status_var,
        font=("Microsoft YaHei", 10, "bold"),
        fg="#27ae60"
    )
    status_label.pack()
    
    # 测试步骤跟踪
    steps_frame = tk.Frame(test_frame)
    steps_frame.pack(fill=tk.X, pady=5)
    
    steps_text = tk.Text(
        steps_frame,
        height=8,
        width=80,
        font=("Consolas", 9),
        bg="#f8f9fa",
        fg="#2c3e50"
    )
    steps_text.pack(fill=tk.BOTH, expand=True)
    
    def log_step(message):
        """记录测试步骤"""
        timestamp = time.strftime("%H:%M:%S")
        steps_text.insert(tk.END, f"[{timestamp}] {message}\n")
        steps_text.see(tk.END)
        steps_text.update()
    
    def start_test():
        """开始测试"""
        log_step("🚀 开始主线剧情衔接功能测试")
        status_var.set("测试进行中...")
        
        # 清空之前的消息
        app.message_text.config(state=tk.NORMAL)
        app.message_text.delete(1.0, tk.END)
        app.message_text.config(state=tk.DISABLED)
        
        log_step("📤 模拟接收AI响应...")
        # 模拟接收到AI响应
        app._display_message("assistant", test_response)
        app.history_manager.add_message("assistant", test_response)
        
        log_step("🔍 触发对话检测...")
        # 解析并显示（这会触发对话窗口）
        result = app._check_dialogue_trigger(test_response)
        
        if result:
            log_step("✅ 对话触发成功，对话窗口已创建")
            log_step("👀 请观察对话窗口的自动开场和后续对话")
            log_step("⏳ 对话结束后将自动测试主线剧情衔接...")
            status_var.set("对话窗口已创建，等待对话完成...")
        else:
            log_step("❌ 对话触发失败")
            status_var.set("测试失败：对话触发失败")
    
    def reset_test():
        """重置测试"""
        log_step("🔄 重置测试环境")
        status_var.set("等待开始测试...")
        steps_text.delete(1.0, tk.END)
    
    # 测试按钮
    button_frame = tk.Frame(test_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    start_button = tk.Button(
        button_frame,
        text="🚀 开始测试",
        command=start_test,
        bg="#3498db",
        fg="white",
        font=("Microsoft YaHei", 11, "bold"),
        padx=20,
        pady=5
    )
    start_button.pack(side=tk.LEFT, padx=5)
    
    reset_button = tk.Button(
        button_frame,
        text="🔄 重置测试",
        command=reset_test,
        bg="#95a5a6",
        fg="white",
        font=("Microsoft YaHei", 11, "bold"),
        padx=20,
        pady=5
    )
    reset_button.pack(side=tk.LEFT, padx=5)
    
    # 监控对话完成事件
    original_callback = app._on_dialogue_complete
    
    def enhanced_callback(dialogue_history, npc_name):
        """增强的对话完成回调"""
        log_step(f"🎯 对话完成回调被调用 - NPC: {npc_name}")
        log_step(f"📝 对话历史长度: {len(dialogue_history)} 字符")
        
        # 调用原始回调
        try:
            original_callback(dialogue_history, npc_name)
            log_step("✅ 原始回调执行成功")
            log_step("🔄 主线剧情衔接已触发，请观察主聊天窗口...")
            status_var.set("主线剧情衔接中...")
            
            # 延迟检查结果
            def check_result():
                log_step("🔍 检查主线剧情衔接结果...")
                # 这里可以添加更多的结果检查逻辑
                status_var.set("测试完成，请查看主聊天窗口的衔接结果")
                log_step("✅ 主线剧情衔接测试完成")
            
            app.root.after(3000, check_result)  # 3秒后检查结果
            
        except Exception as e:
            log_step(f"❌ 回调执行失败: {e}")
            status_var.set("测试失败：回调执行失败")
    
    # 替换回调函数
    app._on_dialogue_complete = enhanced_callback
    
    print("测试GUI创建完成")
    print("=" * 80)
    print("🔧 主线剧情衔接功能测试")
    print("=" * 80)
    print("本测试将验证以下功能：")
    print("1. 对话完成后是否正确调用回调函数")
    print("2. 是否正确触发主线剧情衔接")
    print("3. 衔接API请求是否正确发送")
    print("4. 主聊天窗口是否正确显示衔接结果")
    print()
    print("请在GUI中点击'开始测试'按钮开始测试！")
    print("=" * 80)
    
    return app


def main():
    """主函数"""
    print("🔧 启动主线剧情衔接功能测试程序")
    print("=" * 100)
    
    try:
        # 创建测试GUI
        app = create_test_gui()
        
        # 运行GUI主循环
        app.run()
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
        return 0
    except Exception as e:
        print(f"💥 测试程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("测试完成")
    return 0


if __name__ == "__main__":
    sys.exit(main())
