#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试豆包模型API修复效果
验证max_tokens和thinking参数的修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_max_tokens_validation():
    """测试max_tokens参数验证"""
    print("=" * 60)
    print("测试max_tokens参数验证")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.volcengine_client import VolcengineAPIClient
        
        client = VolcengineAPIClient()
        
        # 测试不同模型的max_tokens限制
        test_cases = [
            ("doubao-seed-1-6-250615", 65536, 16384),  # 标准版：应该被限制到16384
            ("doubao-seed-1-6-thinking-250715", 65536, 32768),  # 思考版：应该被限制到32768
            ("doubao-seed-1-6-flash-250715", 65536, 16384),  # 闪电版：应该被限制到16384
            ("doubao-seed-1-6-thinking-250715", 16384, 16384),  # 思考版：16384应该保持不变
        ]
        
        print("1. 测试max_tokens验证功能...")
        for model, input_tokens, expected_tokens in test_cases:
            result = client._validate_max_tokens(model, input_tokens)
            status = "✅" if result == expected_tokens else "❌"
            print(f"  {status} {model}: {input_tokens} -> {result} (期望: {expected_tokens})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_thinking_model_detection():
    """测试thinking模型检测"""
    print("\n" + "=" * 60)
    print("测试thinking模型检测")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.volcengine_client import VolcengineAPIClient
        
        client = VolcengineAPIClient()
        
        # 测试模型的thinking支持检测
        test_models = [
            ("doubao-seed-1-6-250615", False),  # 标准版：不支持thinking
            ("doubao-seed-1-6-thinking-250715", True),  # 思考版：支持thinking
            ("doubao-seed-1-6-flash-250715", False),  # 闪电版：不支持thinking
            ("deepseek-reasoner", False),  # DeepSeek：不支持thinking（使用不同机制）
        ]
        
        print("1. 测试thinking模型检测...")
        for model, expected_support in test_models:
            result = client._is_doubao_thinking_model(model)
            status = "✅" if result == expected_support else "❌"
            support_text = "支持" if result else "不支持"
            expected_text = "支持" if expected_support else "不支持"
            print(f"  {status} {model}: {support_text} thinking (期望: {expected_text})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_thinking_mode():
    """测试GUI thinking模式判断"""
    print("\n" + "=" * 60)
    print("测试GUI thinking模式判断")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()
        
        # 测试thinking模式判断
        test_models = [
            ("doubao-seed-1-6-250615", "disabled"),  # 标准版：disabled
            ("doubao-seed-1-6-thinking-250715", "enabled"),  # 思考版：enabled
            ("doubao-seed-1-6-flash-250715", "disabled"),  # 闪电版：disabled
            ("deepseek-reasoner", "disabled"),  # DeepSeek：disabled
        ]
        
        print("1. 测试GUI thinking模式判断...")
        for model, expected_mode in test_models:
            result = gui._get_thinking_mode_for_model(model)
            status = "✅" if result == expected_mode else "❌"
            print(f"  {status} {model}: {result} (期望: {expected_mode})")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_configs():
    """测试模型配置"""
    print("\n" + "=" * 60)
    print("测试模型配置")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.volcengine_client import VolcengineAPIClient
        
        client = VolcengineAPIClient()
        
        # 检查关键模型的配置
        key_models = [
            "doubao-seed-1-6-250615",
            "doubao-seed-1-6-thinking-250715",
            "doubao-seed-1-6-flash-250715"
        ]
        
        print("1. 检查模型配置...")
        for model in key_models:
            config = client.get_model_config(model)
            max_tokens = config.get("max_tokens", "未配置")
            print(f"  📋 {model}:")
            print(f"     max_tokens: {max_tokens}")
            print(f"     name: {config.get('name', '未配置')}")
            
            # 验证max_tokens不超过32768
            if isinstance(max_tokens, int) and max_tokens <= 32768:
                print(f"     ✅ max_tokens配置合理")
            else:
                print(f"     ❌ max_tokens配置可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def simulate_api_request():
    """模拟API请求测试"""
    print("\n" + "=" * 60)
    print("模拟API请求测试")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.volcengine_client import VolcengineAPIClient
        from deepseek_chat_client.api_client import ChatMessage
        
        client = VolcengineAPIClient()
        
        if not client.is_available():
            print("⚠️  火山引擎API不可用，跳过API请求测试")
            return True
        
        # 测试消息
        messages = [ChatMessage(role="user", content="你好，这是一个测试消息")]
        
        # 测试豆包种子1.6思考版
        model = "doubao-seed-1-6-thinking-250715"
        print(f"1. 测试模型: {model}")
        print("   构建请求参数...")
        
        # 模拟请求构建过程（不实际发送）
        request_data = {
            "model": model,
            "messages": [msg.to_dict() for msg in messages],
            "stream": True
        }
        
        # 测试max_tokens验证
        test_max_tokens = 65536
        validated_max_tokens = client._validate_max_tokens(model, test_max_tokens)
        request_data["max_tokens"] = validated_max_tokens
        
        print(f"   原始max_tokens: {test_max_tokens}")
        print(f"   验证后max_tokens: {validated_max_tokens}")
        
        # 测试thinking参数
        thinking_mode = "enabled"
        if client._is_doubao_thinking_model(model):
            request_data["thinking"] = {"type": thinking_mode}
            print(f"   添加thinking参数: {thinking_mode}")
        else:
            print(f"   模型不支持thinking参数")
        
        print(f"   ✅ 请求参数构建成功")
        print(f"   📋 最终请求数据: {request_data}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔧 开始测试豆包模型API修复效果")
    print("=" * 100)
    
    print("📋 修复内容：")
    print("1. 修正豆包种子1.6思考版的max_tokens限制（65536 -> 32768）")
    print("2. 限制thinking参数只对真正支持的模型使用")
    print("3. 添加max_tokens自动验证和调整功能")
    print("4. 改进thinking模式判断逻辑")
    print()
    
    # 运行测试
    tests = [
        ("max_tokens参数验证", test_max_tokens_validation),
        ("thinking模型检测", test_thinking_model_detection),
        ("GUI thinking模式判断", test_gui_thinking_mode),
        ("模型配置检查", test_model_configs),
        ("API请求模拟", simulate_api_request),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 100)
    print("📊 修复验证结果汇总")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 豆包模型API问题修复成功！")
        print("\n✨ 修复效果：")
        print("  ✅ max_tokens参数不再超出模型限制")
        print("  ✅ thinking参数只对支持的模型使用")
        print("  ✅ 自动验证和调整参数值")
        print("  ✅ 改进的错误处理和调试信息")
        
        print("\n🚀 现在可以正常使用豆包种子1.6思考版了！")
        print("\n💡 使用建议：")
        print("  • 豆包种子1.6思考版现在使用'enabled'思考模式")
        print("  • max_tokens会自动限制在32768以内")
        print("  • 其他豆包模型不会使用thinking参数")
        print("  • 系统会自动处理参数验证和调整")
        
        return 0
    else:
        print("\n❌ 部分修复验证失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    sys.exit(main())
