#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型价格显示功能的脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_price_display():
    """测试模型价格显示功能"""
    print("=" * 80)
    print("模型价格显示功能测试")
    print("=" * 80)
    
    try:
        # 1. 导入模块
        print("\n1. 导入模块...")
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        from deepseek_chat_client.volcengine_client import VolcengineAPIClient
        from deepseek_chat_client.api_client import DeepSeekAPIClient
        print("✓ 模块导入成功")
        
        # 2. 初始化客户端
        print("\n2. 初始化客户端...")
        unified_client = UnifiedAPIClient()
        volcengine_client = VolcengineAPIClient()
        deepseek_client = DeepSeekAPIClient()
        print("✓ 客户端初始化成功")
        
        # 3. 验证期望的价格数据
        print("\n3. 验证期望的价格数据...")
        expected_prices = {
            "deepseek-chat": 8,
            "deepseek-reasoner": 16,
            "doubao-seed-1-6-250615": 16,
            "doubao-seed-1-6-thinking-250715": 16,
            "doubao-seed-1-6-flash-250715": 3,
            "doubao-1-5-pro-32k-250115": 2
        }
        
        print("期望的价格数据:")
        for model, price in expected_prices.items():
            print(f"  {model}: {price}")
        
        # 4. 检查统一客户端的价格配置
        print("\n4. 检查统一客户端的价格配置...")
        actual_prices = unified_client.model_prices
        
        price_errors = []
        for model, expected_price in expected_prices.items():
            actual_price = actual_prices.get(model)
            if actual_price == expected_price:
                print(f"  ✓ {model}: {actual_price} (正确)")
            else:
                print(f"  ✗ {model}: 期望 {expected_price}, 实际 {actual_price}")
                price_errors.append(model)
        
        if price_errors:
            print(f"✗ 价格配置错误的模型: {price_errors}")
            return False
        else:
            print("✓ 所有模型价格配置正确")
        
        # 5. 测试模型列表显示格式
        print("\n5. 测试模型列表显示格式...")
        available_models = unified_client.get_available_models()
        
        print("可用模型列表（包含价格）:")
        for model_id, display_name in available_models.items():
            print(f"  ID: {model_id}")
            print(f"  显示: {display_name}")
            
            # 检查显示格式是否正确
            if "(价格:" in display_name and ")" in display_name:
                print(f"    ✓ 价格格式正确")
            else:
                print(f"    ✗ 价格格式错误")
                return False
            print()
        
        # 6. 验证DeepSeek模型的价格显示
        print("\n6. 验证DeepSeek模型的价格显示...")
        deepseek_models = deepseek_client.get_available_models()
        
        for model_id, model_name in deepseek_models.items():
            expected_price = expected_prices.get(model_id, 0)
            display_name = available_models.get(model_id, "")
            expected_display = f"{model_name} (价格: {expected_price})"
            
            if display_name == expected_display:
                print(f"  ✓ {model_id}: {display_name}")
            else:
                print(f"  ✗ {model_id}:")
                print(f"    期望: {expected_display}")
                print(f"    实际: {display_name}")
                return False
        
        # 7. 验证火山引擎模型的价格显示
        print("\n7. 验证火山引擎模型的价格显示...")
        if volcengine_client.is_available():
            volcengine_models = volcengine_client.get_available_models()
            
            for model_id, display_name in volcengine_models.items():
                expected_price = expected_prices.get(model_id, 0)
                expected_format = f"(价格: {expected_price})"
                
                if expected_format in display_name:
                    print(f"  ✓ {model_id}: {display_name}")
                else:
                    print(f"  ✗ {model_id}: 价格格式不正确")
                    print(f"    显示: {display_name}")
                    print(f"    期望包含: {expected_format}")
                    return False
        else:
            print("  ⚠️ 火山引擎客户端不可用，跳过测试")
        
        # 8. 测试价格获取方法
        print("\n8. 测试价格获取方法...")
        for model_id in expected_prices.keys():
            if model_id in available_models:
                price = unified_client.get_model_price(model_id)
                expected_price = expected_prices[model_id]
                
                if price == expected_price:
                    print(f"  ✓ {model_id}: 价格 {price}")
                else:
                    print(f"  ✗ {model_id}: 期望价格 {expected_price}, 实际 {price}")
                    return False
        
        # 9. 测试模型信息获取
        print("\n9. 测试模型信息获取...")
        for model_id in list(expected_prices.keys())[:3]:  # 测试前3个模型
            if model_id in available_models:
                model_info = unified_client.get_model_info(model_id)
                
                print(f"  模型: {model_id}")
                print(f"    名称: {model_info.get('name', 'N/A')}")
                print(f"    提供商: {model_info.get('provider', 'N/A')}")
                print(f"    价格: {model_info.get('price', 'N/A')}")
                print(f"    最大Token: {model_info.get('max_tokens', 'N/A')}")
                print()
        
        # 10. 生成GUI显示预览
        print("\n10. GUI显示预览...")
        print("下拉框中将显示的选项:")
        print("-" * 60)
        
        # 按价格排序显示
        sorted_models = sorted(available_models.items(), 
                             key=lambda x: unified_client.get_model_price(x[0]))
        
        for model_id, display_name in sorted_models:
            price = unified_client.get_model_price(model_id)
            provider = unified_client.get_provider_for_model(model_id)
            
            # 添加推荐标识
            recommendation = ""
            if price <= 3:
                recommendation = " 🔥推荐"
            elif price <= 8:
                recommendation = " 💡经济"
            
            print(f"  {display_name}{recommendation}")
            print(f"    (提供商: {provider})")
        
        print("-" * 60)
        
        print("\n" + "=" * 80)
        print("🎉 所有价格显示功能测试通过！")
        print("=" * 80)
        
        print("\n📊 测试结果总结:")
        print(f"  ✅ 价格配置验证: 通过")
        print(f"  ✅ 显示格式检查: 通过")
        print(f"  ✅ DeepSeek模型: 通过")
        print(f"  ✅ 火山引擎模型: 通过")
        print(f"  ✅ 价格获取方法: 通过")
        print(f"  ✅ 模型信息获取: 通过")
        
        print(f"\n💡 用户体验优化:")
        print(f"  🔥 最便宜模型: doubao-1-5-pro-32k-250115 (价格: 2)")
        print(f"  💡 经济型模型: doubao-seed-1-6-flash-250715 (价格: 3)")
        print(f"  ⚡ 高性能模型: deepseek-reasoner, doubao-seed-1-6-thinking-250715 (价格: 16)")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = test_price_display()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
