#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动测试功能验证脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui_import():
    """测试GUI模块导入"""
    try:
        from deepseek_chat_client.gui import ChatGUI
        print("✓ GUI模块导入成功")
        return True
    except Exception as e:
        print(f"✗ GUI模块导入失败: {e}")
        return False

def test_auto_test_variables():
    """测试自动测试变量初始化"""
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建GUI实例（不运行）
        gui = ChatGUI()
        
        # 检查自动测试相关变量
        required_vars = [
            'auto_select_enabled',
            'auto_execute_enabled', 
            'auto_execute_rounds',
            'current_auto_round',
            'auto_execute_timer_id',
            'auto_select_delay',
            'auto_test_start_time',
            'auto_test_log_file'
        ]
        
        for var_name in required_vars:
            if not hasattr(gui, var_name):
                print(f"✗ 缺少变量: {var_name}")
                return False
        
        print("✓ 自动测试变量初始化成功")
        
        # 检查默认值
        assert gui.auto_execute_rounds.get() == 10, "默认回合数应为10"
        assert gui.auto_select_delay.get() == 3, "默认延迟应为3秒"
        assert gui.current_auto_round == 0, "初始回合计数应为0"
        
        print("✓ 默认值设置正确")
        return True
        
    except Exception as e:
        print(f"✗ 自动测试变量测试失败: {e}")
        return False

def test_auto_test_methods():
    """测试自动测试方法存在性"""
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        gui = ChatGUI()
        
        # 检查必要的方法
        required_methods = [
            '_create_auto_test_panel',
            '_on_auto_select_toggle',
            '_on_auto_execute_toggle',
            '_update_auto_test_status',
            '_toggle_auto_test',
            '_start_auto_test',
            '_stop_auto_test',
            '_execute_auto_round',
            '_perform_auto_select',
            '_execute_selected_option',
            '_send_auto_message',
            '_wait_for_response_and_continue',
            '_check_if_game_ended',
            '_init_auto_test_log',
            '_log_auto_test',
            '_log_auto_test_summary',
            '_check_auto_select_trigger'
        ]
        
        for method_name in required_methods:
            if not hasattr(gui, method_name):
                print(f"✗ 缺少方法: {method_name}")
                return False
            if not callable(getattr(gui, method_name)):
                print(f"✗ {method_name} 不是可调用方法")
                return False
        
        print("✓ 自动测试方法检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 自动测试方法测试失败: {e}")
        return False

def test_log_functionality():
    """测试日志功能"""
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        gui = ChatGUI()
        
        # 测试日志初始化
        gui._init_auto_test_log()
        
        if gui.auto_test_log_file:
            print(f"✓ 日志文件创建成功: {gui.auto_test_log_file}")
            
            # 测试日志记录
            gui._log_auto_test("测试日志消息", "INFO")
            
            # 检查日志文件是否存在
            if gui.auto_test_log_file.exists():
                print("✓ 日志文件写入成功")
                
                # 读取并验证日志内容
                with open(gui.auto_test_log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "测试日志消息" in content:
                        print("✓ 日志内容验证成功")
                    else:
                        print("✗ 日志内容验证失败")
                        return False
            else:
                print("✗ 日志文件不存在")
                return False
        else:
            print("✗ 日志文件初始化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 日志功能测试失败: {e}")
        return False

def test_ui_elements():
    """测试UI元素创建"""
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        gui = ChatGUI()
        
        # 检查自动测试控制元素
        required_ui_elements = [
            'auto_select_checkbox',
            'auto_execute_checkbox',
            'auto_test_control_btn',
            'auto_test_status_label'
        ]
        
        for element_name in required_ui_elements:
            if not hasattr(gui, element_name):
                print(f"✗ 缺少UI元素: {element_name}")
                return False
        
        print("✓ UI元素创建成功")
        return True
        
    except Exception as e:
        print(f"✗ UI元素测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始自动测试功能验证...")
    print("=" * 50)
    
    tests = [
        ("GUI模块导入", test_gui_import),
        ("自动测试变量", test_auto_test_variables),
        ("自动测试方法", test_auto_test_methods),
        ("日志功能", test_log_functionality),
        ("UI元素", test_ui_elements)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！自动测试功能实现成功！")
        return True
    else:
        print("❌ 部分测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
