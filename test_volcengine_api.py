#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎（豆包）大模型API连接测试脚本
测试API连接性并获取可用模型列表
"""

import os
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_environment():
    """加载环境变量"""
    try:
        from dotenv import load_dotenv
        env_path = project_root / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            print(f"✓ 已加载环境变量文件: {env_path}")
        else:
            print(f"⚠ 环境变量文件不存在: {env_path}")
            return False
        return True
    except ImportError:
        print("⚠ python-dotenv 未安装，尝试直接读取系统环境变量")
        return True

def check_dependencies():
    """检查必要的依赖项"""
    print("=" * 60)
    print("检查依赖项...")
    print("=" * 60)

    dependencies = {
        'requests': 'HTTP客户端库',
        'dotenv': '环境变量加载器'
    }

    missing_deps = []

    for dep, desc in dependencies.items():
        try:
            __import__(dep)
            print(f"✓ {dep}: {desc}")
        except ImportError:
            print(f"✗ {dep}: {desc} - 未安装")
            missing_deps.append(dep)

    if missing_deps:
        print(f"\n缺少依赖项: {', '.join(missing_deps)}")
        print("请运行以下命令安装:")
        for dep in missing_deps:
            if dep == 'dotenv':
                print(f"  pip install python-dotenv")
            else:
                print(f"  pip install {dep}")
        return False

    print("✓ 所有依赖项检查通过")
    return True

def get_api_key():
    """获取API密钥"""
    api_key = os.getenv('volcengine_API_KEY')
    if not api_key:
        print("✗ 未找到火山引擎API密钥")
        print("请在.env文件中设置: volcengine_API_KEY='your_api_key_here'")
        return None
    
    if api_key.strip() == '':
        print("✗ 火山引擎API密钥为空")
        print("请在.env文件中设置有效的API密钥")
        return None
    
    print(f"✓ 已获取API密钥: {api_key[:10]}...")
    return api_key

def test_volcengine_connection(api_key: str):
    """测试火山引擎API连接"""
    print("\n" + "=" * 60)
    print("测试火山引擎API连接...")
    print("=" * 60)

    try:
        import requests

        # 火山引擎方舟API端点
        url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        # 测试用户已开通的模型（使用正确的小写格式）
        test_models = [
            "doubao-seed-1.6-flash",
            "doubao-seed-1.6-thinking",
            "doubao-seed-1.6",
            "doubao-1-5-pro-32k-250115",
            "doubao-pro-4k",
            "doubao-lite-4k",
            "doubao-pro-32k",
            "doubao-1-5-lite-32k-250115"
        ]

        success_model = None

        for model_name in test_models:
            print(f"  尝试模型: {model_name}")

            test_data = {
                "messages": [
                    {
                        "content": "You are a helpful assistant.",
                        "role": "system"
                    },
                    {
                        "content": "hello",
                        "role": "user"
                    }
                ],
                "model": model_name,
                "stream": False,
                "max_tokens": 50
            }

            print("✓ 准备发送API请求...")
            print(f"  端点: {url}")

            # 发送请求
            response = requests.post(url, headers=headers, json=test_data, timeout=30)

            print(f"  收到响应，状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print(f"✓ API连接测试成功，使用模型: {model_name}")

                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"  响应内容: {content}")

                    if 'usage' in result:
                        usage = result['usage']
                        print(f"  Token使用: 输入={usage.get('prompt_tokens', 0)}, 输出={usage.get('completion_tokens', 0)}, 总计={usage.get('total_tokens', 0)}")

                success_model = model_name
                return True, (result, success_model)

            else:
                try:
                    error_info = response.json()
                    print(f"  失败: {error_info.get('error', {}).get('message', '未知错误')}")
                except:
                    print(f"  失败: {response.text}")

                # 如果是模型未激活错误，继续尝试下一个模型
                if response.status_code == 404:
                    continue
                else:
                    # 其他错误直接返回
                    break

        # 如果所有模型都失败了
        print("✗ 所有测试模型都无法使用")
        print("\n📋 模型激活指南:")
        print("1. 访问火山引擎控制台: https://console.volcengine.com/ark")
        print("2. 进入「方舟」产品页面")
        print("3. 在「模型广场」中选择并激活所需模型")
        print("4. 常用模型推荐:")
        print("   - doubao-pro-4k: 适合日常对话")
        print("   - doubao-pro-32k: 适合长文本处理")
        print("   - doubao-1-5-pro-32k-250115: 最新版本，性能更好")
        print("5. 激活后等待几分钟即可使用")
        return False, None

    except requests.exceptions.Timeout:
        print("✗ 请求超时")
        print("  建议: 检查网络连接或增加超时时间")
        return False, None
    except requests.exceptions.ConnectionError:
        print("✗ 连接错误")
        print("  建议: 检查网络连接和防火墙设置")
        return False, None
    except Exception as e:
        print(f"✗ 发生未预期错误: {e}")
        return False, None

def get_available_models():
    """获取可用模型列表"""
    print("\n" + "=" * 60)
    print("获取可用模型列表...")
    print("=" * 60)

    # 火山引擎方舟平台的可用模型列表（包含用户已开通的模型）
    common_models = [
        {
            "id": "doubao-seed-1.6-flash",
            "name": "豆包种子1.6闪电版",
            "description": "最新的豆包种子1.6闪电版，响应速度快，适合实时对话",
            "max_tokens": 8192,
            "type": "chat",
            "context_length": 8192,
            "status": "✅ 已开通",
            "pricing": "输入: 0.00007-0.0003元/千tokens, 输出: 0.00075-0.0030元/千tokens"
        },
        {
            "id": "doubao-seed-1.6-thinking",
            "name": "豆包种子1.6思考版",
            "description": "豆包种子1.6思考版，具备深度推理能力，适合复杂任务",
            "max_tokens": 8192,
            "type": "chat",
            "context_length": 8192,
            "status": "✅ 已开通",
            "pricing": "输入: 0.0004-0.0012元/千tokens, 输出: 0.0040-0.0120元/千tokens"
        },
        {
            "id": "doubao-seed-1.6",
            "name": "豆包种子1.6标准版",
            "description": "豆包种子1.6标准版，平衡性能和成本",
            "max_tokens": 8192,
            "type": "chat",
            "context_length": 8192,
            "status": "✅ 已开通",
            "pricing": "标准定价"
        },
        {
            "id": "doubao-1-5-pro-32k-250115",
            "name": "豆包1.5 Pro 32K",
            "description": "豆包1.5专业版，支持32K上下文长度",
            "max_tokens": 32768,
            "type": "chat",
            "context_length": 32768,
            "status": "❓ 未知",
            "pricing": "标准定价"
        },
        {
            "id": "doubao-1-5-lite-32k-250115",
            "name": "豆包1.5 Lite 32K",
            "description": "豆包1.5轻量版，支持32K上下文长度",
            "max_tokens": 32768,
            "type": "chat",
            "context_length": 32768,
            "status": "❓ 未知",
            "pricing": "标准定价"
        },
        {
            "id": "doubao-pro-4k",
            "name": "豆包专业版 4K",
            "description": "专业级对话模型，适合复杂任务",
            "max_tokens": 4096,
            "type": "chat",
            "context_length": 4096,
            "status": "❓ 未知",
            "pricing": "标准定价"
        },
        {
            "id": "doubao-lite-4k",
            "name": "豆包轻量版 4K",
            "description": "轻量级对话模型，适合简单对话场景",
            "max_tokens": 4096,
            "type": "chat",
            "context_length": 4096,
            "status": "❓ 未知",
            "pricing": "标准定价"
        },
        {
            "id": "doubao-pro-32k",
            "name": "豆包专业版 32K",
            "description": "支持长文本的专业级对话模型",
            "max_tokens": 32768,
            "type": "chat",
            "context_length": 32768,
            "status": "❓ 未知",
            "pricing": "标准定价"
        }
    ]

    print("火山引擎方舟平台可用模型列表:\n")

    for i, model in enumerate(common_models, 1):
        print(f"{i}. 模型ID: {model['id']}")
        print(f"   名称: {model['name']}")
        print(f"   描述: {model['description']}")
        print(f"   最大Token: {model['max_tokens']}")
        print(f"   上下文长度: {model['context_length']}")
        print(f"   类型: {model['type']}")
        print(f"   状态: {model['status']}")
        print(f"   定价: {model['pricing']}")
        print()

    print("📝 使用说明:")
    print("1. 火山引擎方舟使用标准模型名称，无需创建端点")
    print("2. 支持流式和非流式响应")
    print("3. 兼容OpenAI API格式")
    print("4. 推荐使用最新的1.5版本模型获得更好性能")

    return common_models

def test_model_capabilities(api_key: str, models: List[Dict[str, Any]]):
    """测试模型能力"""
    print("\n" + "=" * 60)
    print("测试模型能力...")
    print("=" * 60)

    import requests

    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    test_message = "你好，请简单介绍一下自己。"

    for model in models[:2]:  # 只测试前两个模型
        print(f"\n测试模型: {model['name']} ({model['id']})")
        print("-" * 40)

        try:
            test_data = {
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": test_message}
                ],
                "model": model['id'],
                "max_tokens": 100,
                "temperature": 0.7,
                "stream": False
            }

            response = requests.post(url, headers=headers, json=test_data, timeout=30)

            if response.status_code == 200:
                result = response.json()

                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"✓ 响应成功")
                    print(f"  内容: {content[:100]}{'...' if len(content) > 100 else ''}")

                    if 'usage' in result:
                        usage = result['usage']
                        print(f"  Token使用: 输入={usage.get('prompt_tokens', 0)}, 输出={usage.get('completion_tokens', 0)}, 总计={usage.get('total_tokens', 0)}")
                else:
                    print(f"✗ 响应格式异常: {result}")
            else:
                print(f"✗ 测试失败，状态码: {response.status_code}")
                try:
                    error_info = response.json()
                    print(f"  错误信息: {error_info}")
                except:
                    print(f"  错误信息: {response.text}")

        except requests.exceptions.Timeout:
            print("✗ 请求超时")
        except requests.exceptions.ConnectionError:
            print("✗ 连接错误")
        except Exception as e:
            print(f"✗ 测试失败: {str(e)}")

        time.sleep(1)  # 避免请求过于频繁

def main():
    """主函数"""
    print("=" * 60)
    print("火山引擎（豆包）大模型API连接测试")
    print("=" * 60)
    
    # 1. 加载环境变量
    if not load_environment():
        return False
    
    # 2. 检查依赖项
    if not check_dependencies():
        return False
    
    # 3. 获取API密钥
    api_key = get_api_key()
    if not api_key:
        return False
    
    # 4. 测试API连接
    success, result = test_volcengine_connection(api_key)
    if not success:
        return False

    # 5. 获取模型列表
    models = get_available_models()

    # 6. 测试模型能力（可选）
    if success and result:
        try:
            # 如果连接测试成功，使用成功的模型进行进一步测试
            if isinstance(result, tuple) and len(result) == 2:
                _, success_model = result
                print(f"\n使用成功连接的模型 {success_model} 进行进一步测试...")
            test_model_capabilities(api_key, models)
        except Exception as e:
            print(f"⚠ 模型能力测试跳过: {e}")
    
    print("\n" + "=" * 60)
    print("✓ 火山引擎API测试完成")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
