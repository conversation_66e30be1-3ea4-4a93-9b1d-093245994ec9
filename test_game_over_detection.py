#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试游戏结束检测功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_game_over_detection():
    """测试游戏结束检测功能"""
    print("=" * 80)
    print("游戏结束检测功能测试")
    print("=" * 80)
    
    try:
        # 1. 导入GUI模块
        print("\n1. 导入GUI模块...")
        from deepseek_chat_client.gui import ChatGUI
        print("✓ GUI模块导入成功")
        
        # 2. 创建GUI实例进行测试
        print("\n2. 创建GUI实例...")
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        print("✓ GUI实例创建成功")
        
        # 3. 测试游戏结束标记检测
        print("\n3. 测试游戏结束标记检测...")
        
        # 测试各种游戏结束标记格式
        test_cases = [
            # 标准格式
            ("包含{gameover}标记的内容", True, "{gameover}"),
            ("包含{GAMEOVER}标记的内容", True, "{GAMEOVER}"),
            ("包含{GameOver}标记的内容", True, "{GameOver}"),
            ("包含{game_over}标记的内容", True, "{game_over}"),
            ("包含{GAME_OVER}标记的内容", True, "{GAME_OVER}"),
            
            # 不包含游戏结束标记
            ("正常的游戏内容，没有结束标记", False, None),
            ("包含gameover但没有大括号", False, None),
            ("包含{other}标记但不是游戏结束", False, None),
            ("", False, None),  # 空内容
        ]
        
        for content, expected, marker in test_cases:
            result = gui._check_game_over(content)
            if result == expected:
                status = "✓"
                if expected:
                    print(f"  {status} 正确检测到游戏结束标记: {marker}")
                else:
                    print(f"  {status} 正确识别非游戏结束内容")
            else:
                print(f"  ✗ 检测失败 - 内容: '{content[:30]}...', 期望: {expected}, 实际: {result}")
                return False
        
        # 4. 测试复杂游戏场景中的检测
        print("\n4. 测试复杂游戏场景中的检测...")
        
        # 模拟完整的游戏结束响应
        game_over_response = '''
〖第一百章100/100〗第100回，〖天道之巅〗经过漫长的修炼历程，你终于达到了修仙的最高境界。

【张三】修为已达天仙境界，成为了传说中的仙人。

「内心活动」终于完成了修仙之路，这一路走来真是不容易啊。

「系统提示」恭喜你完成了修仙之旅！

{gameover}

游戏已结束，感谢您的游玩！
'''
        
        if gui._check_game_over(game_over_response):
            print("✓ 复杂游戏场景中正确检测到游戏结束")
        else:
            print("✗ 复杂游戏场景中未能检测到游戏结束")
            return False
        
        # 5. 测试包含快捷回复但有游戏结束标记的情况
        print("\n5. 测试游戏结束优先级...")
        
        mixed_response = '''
游戏即将结束...

{gameover}

```json
{"quick_replies":["重新开始", "查看成就", "退出游戏"]}
```
'''
        
        # 模拟完整的解析流程
        print("  测试完整解析流程...")
        
        # 创建一个模拟的消息文本控件用于测试
        import tkinter as tk
        test_root = tk.Tk()
        test_root.withdraw()
        
        # 临时替换GUI的消息文本控件
        original_message_text = gui.message_text
        gui.message_text = tk.Text(test_root)
        gui.message_text.config = lambda **kwargs: None  # 模拟config方法
        
        # 测试解析方法
        try:
            # 这应该检测到游戏结束并跳过快捷回复解析
            gui._parse_and_show_quick_replies(mixed_response)
            print("✓ 游戏结束检测优先于快捷回复解析")
        except Exception as e:
            print(f"✗ 解析过程中出错: {e}")
            return False
        finally:
            # 恢复原始的消息文本控件
            gui.message_text = original_message_text
            test_root.destroy()
        
        # 6. 测试游戏结束处理方法
        print("\n6. 测试游戏结束处理方法...")
        
        # 测试_handle_game_over方法
        try:
            # 创建临时的状态变量
            original_status_var = gui.status_var
            gui.status_var = tk.StringVar()
            
            # 创建临时的消息文本控件
            test_root2 = tk.Tk()
            test_root2.withdraw()
            gui.message_text = tk.Text(test_root2)
            
            # 调用游戏结束处理
            gui._handle_game_over()
            
            # 检查状态是否更新
            if gui.status_var.get() == "游戏已结束":
                print("✓ 游戏结束状态更新正确")
            else:
                print(f"✗ 游戏结束状态更新错误: {gui.status_var.get()}")
                return False
            
            print("✓ 游戏结束处理方法执行成功")
            
        except Exception as e:
            print(f"✗ 游戏结束处理方法执行失败: {e}")
            return False
        finally:
            # 清理
            gui.status_var = original_status_var
            test_root2.destroy()
        
        # 7. 测试边界情况
        print("\n7. 测试边界情况...")
        
        edge_cases = [
            # 大小写混合
            "游戏内容 {GameOVER} 结束",  # 这个不应该被检测到
            "游戏内容 {gameover} 结束",  # 这个应该被检测到
            
            # 多个标记
            "内容 {gameover} 更多内容 {GAMEOVER} 结束",  # 应该被检测到
            
            # 标记在不同位置
            "{gameover} 开头就是游戏结束",  # 应该被检测到
            "游戏结束在最后 {gameover}",  # 应该被检测到
            "中间有 {gameover} 游戏结束标记",  # 应该被检测到
        ]
        
        expected_results = [False, True, True, True, True, True]
        
        for i, (content, expected) in enumerate(zip(edge_cases, expected_results)):
            result = gui._check_game_over(content)
            if result == expected:
                print(f"✓ 边界情况 {i+1} 检测正确")
            else:
                print(f"✗ 边界情况 {i+1} 检测错误 - 内容: '{content}', 期望: {expected}, 实际: {result}")
                return False
        
        # 清理
        gui.root.destroy()
        
        print("\n" + "=" * 80)
        print("🎉 所有游戏结束检测功能测试通过！")
        print("=" * 80)
        
        print("\n📊 测试结果总结:")
        print("  ✅ 游戏结束标记检测: 正确")
        print("  ✅ 复杂场景检测: 正确")
        print("  ✅ 优先级处理: 正确")
        print("  ✅ 游戏结束处理: 正确")
        print("  ✅ 边界情况处理: 正确")
        
        print("\n💡 功能特性:")
        print("  🎯 多格式支持: 支持5种不同的游戏结束标记格式")
        print("  🚀 优先检测: 游戏结束检测优先于快捷回复解析")
        print("  🛡️ 状态管理: 自动更新界面状态和显示游戏结束消息")
        print("  🔍 边界处理: 正确处理各种边界情况和特殊格式")
        
        print("\n🎮 用户体验:")
        print("  1. AI响应包含{gameover}标记时立即停止快捷回复")
        print("  2. 显示清晰的游戏结束提示信息")
        print("  3. 更新状态栏显示游戏已结束")
        print("  4. 防止用户看到不相关的快捷回复选项")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = test_game_over_detection()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
