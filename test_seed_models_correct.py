#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户提供的正确Seed 1.6系列模型名称
"""

import os
import sys
import json
import requests
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_environment():
    """加载环境变量"""
    try:
        from dotenv import load_dotenv
        env_path = project_root / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            return True
        else:
            print(f"⚠ 环境变量文件不存在: {env_path}")
            return False
    except ImportError:
        print("⚠ python-dotenv 未安装")
        return True

def test_seed_models():
    """测试用户提供的正确Seed模型名称"""
    print("=" * 70)
    print("测试用户提供的正确Seed 1.6系列模型名称")
    print("=" * 70)
    
    # 加载环境变量
    load_environment()
    api_key = os.getenv('volcengine_API_KEY')
    
    if not api_key:
        print("✗ 未找到API密钥")
        return False
    
    print(f"✓ 已获取API密钥: {api_key[:10]}...")
    
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 用户提供的正确模型名称
    seed_models = [
        {
            "id": "doubao-seed-1-6-flash-250715",
            "name": "豆包种子1.6闪电版",
            "description": "最新的豆包种子1.6闪电版，响应速度极快",
            "version": "250715",
            "user_opened": True
        },
        {
            "id": "doubao-seed-1-6-thinking-250715",
            "name": "豆包种子1.6思考版",
            "description": "具备深度推理能力的思考模型",
            "version": "250715", 
            "user_opened": True
        },
        {
            "id": "doubao-seed-1-6-250615",
            "name": "豆包种子1.6标准版",
            "description": "平衡性能和成本的标准版本",
            "version": "250615",
            "user_opened": True
        }
    ]
    
    # 同时测试已知可用的模型作为对比
    comparison_models = [
        {
            "id": "doubao-1-5-pro-32k-250115",
            "name": "豆包1.5专业版32K",
            "description": "已验证可用的对比模型",
            "version": "250115",
            "user_opened": False
        }
    ]
    
    all_models = seed_models + comparison_models
    successful_models = []
    failed_models = []
    
    print(f"\n开始测试 {len(all_models)} 个模型...\n")
    
    for i, model in enumerate(all_models, 1):
        status_icon = "🔥" if model["user_opened"] else "📋"
        status_text = "用户已开通" if model["user_opened"] else "对比模型"
        
        print(f"[{i}/{len(all_models)}] {status_icon} 测试模型: {model['id']}")
        print(f"    名称: {model['name']} ({status_text})")
        print(f"    描述: {model['description']}")
        print(f"    版本: {model['version']}")
        
        test_data = {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "你好，请简单介绍一下自己，并说明你的特点。"}
            ],
            "model": model['id'],
            "max_tokens": 150,
            "temperature": 0.7,
            "stream": False
        }
        
        try:
            response = requests.post(url, headers=headers, json=test_data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    usage = result.get('usage', {})
                    
                    print(f"    ✅ 成功！")
                    print(f"    响应: {content[:100]}{'...' if len(content) > 100 else ''}")
                    print(f"    Token: 输入={usage.get('prompt_tokens', 0)}, 输出={usage.get('completion_tokens', 0)}, 总计={usage.get('total_tokens', 0)}")
                    
                    successful_models.append({
                        'model': model,
                        'response': content,
                        'usage': usage
                    })
                else:
                    print(f"    ❌ 响应格式异常")
                    failed_models.append({'model': model, 'error': '响应格式异常'})
            else:
                try:
                    error_info = response.json()
                    error_msg = error_info.get('error', {}).get('message', '未知错误')
                    print(f"    ❌ 失败 (状态码: {response.status_code})")
                    print(f"    错误: {error_msg[:120]}{'...' if len(error_msg) > 120 else ''}")
                    failed_models.append({'model': model, 'error': error_msg})
                except:
                    print(f"    ❌ 失败 (状态码: {response.status_code})")
                    failed_models.append({'model': model, 'error': f'HTTP {response.status_code}'})
                    
        except requests.exceptions.Timeout:
            print(f"    ❌ 请求超时")
            failed_models.append({'model': model, 'error': '请求超时'})
        except requests.exceptions.ConnectionError:
            print(f"    ❌ 连接错误")
            failed_models.append({'model': model, 'error': '连接错误'})
        except Exception as e:
            print(f"    ❌ 异常: {str(e)}")
            failed_models.append({'model': model, 'error': str(e)})
        
        print()  # 空行分隔
        time.sleep(1)  # 避免请求过于频繁
    
    # 显示详细测试结果
    print("=" * 70)
    print("详细测试结果")
    print("=" * 70)
    
    print(f"\n🎉 成功的模型 ({len(successful_models)} 个):")
    if successful_models:
        for item in successful_models:
            model = item['model']
            usage = item['usage']
            print(f"\n✅ {model['id']}")
            print(f"   名称: {model['name']}")
            print(f"   版本: {model['version']}")
            print(f"   用户状态: {'已开通' if model['user_opened'] else '标准模型'}")
            print(f"   响应: {item['response'][:150]}{'...' if len(item['response']) > 150 else ''}")
            print(f"   Token使用: 输入={usage.get('prompt_tokens', 0)}, 输出={usage.get('completion_tokens', 0)}")
    else:
        print("   无成功的模型")
    
    print(f"\n❌ 失败的模型 ({len(failed_models)} 个):")
    if failed_models:
        for item in failed_models:
            model = item['model']
            print(f"\n❌ {model['id']}")
            print(f"   名称: {model['name']}")
            print(f"   版本: {model['version']}")
            print(f"   用户状态: {'已开通' if model['user_opened'] else '标准模型'}")
            print(f"   错误: {item['error'][:150]}{'...' if len(item['error']) > 150 else ''}")
    else:
        print("   无失败的模型")
    
    # 分析结果并提供建议
    print(f"\n" + "=" * 70)
    print("结果分析和建议")
    print("=" * 70)
    
    seed_successful = [item for item in successful_models if item['model']['user_opened']]
    seed_failed = [item for item in failed_models if item['model']['user_opened']]
    
    if seed_successful:
        print(f"\n🎯 用户开通的Seed 1.6系列可用模型 ({len(seed_successful)} 个):")
        for item in seed_successful:
            model = item['model']
            print(f"  ✅ {model['id']} - {model['name']}")
        
        print(f"\n📋 集成建议:")
        print("1. 立即将这些模型集成到聊天客户端中")
        print("2. 这些是您专门开通的高性能模型，应该优先使用")
        print("3. 可以根据不同场景选择不同的模型：")
        print("   - flash版本：适合需要快速响应的场景")
        print("   - thinking版本：适合需要深度思考的复杂任务")
        print("   - 标准版本：适合一般对话场景")
    
    if seed_failed:
        print(f"\n⚠️ 用户开通但仍无法访问的Seed模型 ({len(seed_failed)} 个):")
        for item in seed_failed:
            model = item['model']
            print(f"  ❌ {model['id']} - {model['name']}")
        
        print(f"\n🔍 可能的解决方案:")
        print("1. 检查模型名称是否还有其他变体")
        print("2. 确认API密钥是否有访问这些特定版本的权限")
        print("3. 联系火山引擎技术支持确认正确的调用方式")
    
    # 生成完整的可用模型列表
    if successful_models:
        print(f"\n📝 完整的可用模型列表:")
        print("```python")
        print("VOLCENGINE_AVAILABLE_MODELS = {")
        for item in successful_models:
            model = item['model']
            print(f'    "{model["id"]}": {{')
            print(f'        "name": "{model["name"]}",')
            print(f'        "description": "{model["description"]}",')
            print(f'        "version": "{model["version"]}",')
            print(f'        "user_opened": {str(model["user_opened"]).lower()},')
            print(f'        "max_tokens": 8192,  # 根据实际情况调整')
            print(f'    }},')
        print("}")
        print("```")
    
    return len(seed_successful) > 0

def main():
    """主函数"""
    try:
        success = test_seed_models()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
