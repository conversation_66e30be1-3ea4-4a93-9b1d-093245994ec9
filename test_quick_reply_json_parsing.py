#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试快捷回复JSON解析功能的修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_json_parsing_fixes():
    """测试JSON解析修复功能"""
    print("=" * 80)
    print("快捷回复JSON解析功能修复测试")
    print("=" * 80)
    
    try:
        # 1. 导入GUI模块
        print("\n1. 导入GUI模块...")
        from deepseek_chat_client.gui import ChatGUI
        print("✓ GUI模块导入成功")
        
        # 2. 创建GUI实例进行测试
        print("\n2. 创建GUI实例...")
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏窗口
        print("✓ GUI实例创建成功")
        
        # 3. 测试多JSON对象解析
        print("\n3. 测试多JSON对象解析...")
        
        # 模拟包含多个JSON对象的AI响应
        complex_response = '''
这是一个修仙游戏的回复内容。

{"触发独处聊天程序":["苏瑶", "正道/太清宗/内门核心弟子 筑基九层半步金丹", "苏瑶100", "张轩0", "王虎0"]}

现在为你提供一些快捷回复选项：

```json
{"quick_replies":["询问如何控制灵气注入的量", "询问怎样才能准确绘制符文", "询问调整心态的方法", "表示会注意这些问题并开始练习"]}
```

游戏继续...
'''
        
        # 测试提取功能
        extracted_replies = gui._extract_quick_replies_from_content(complex_response)
        
        expected_replies = [
            "询问如何控制灵气注入的量",
            "询问怎样才能准确绘制符文", 
            "询问调整心态的方法",
            "表示会注意这些问题并开始练习"
        ]
        
        if extracted_replies == expected_replies:
            print("✓ 多JSON对象解析成功")
            print(f"  提取结果: {extracted_replies}")
        else:
            print("✗ 多JSON对象解析失败")
            print(f"  期望: {expected_replies}")
            print(f"  实际: {extracted_replies}")
            return False
        
        # 4. 测试无效选项过滤
        print("\n4. 测试无效选项过滤...")
        
        # 模拟包含无效选项的响应
        invalid_response = '''
```json
{"quick_replies":["苏瑶100", "张轩0", "王虎0", "询问修炼方法", "123", "a", "这是一个非常长的无效选项，超过了100个字符的限制，应该被过滤掉，因为它太长了，不适合作为快捷回复选项显示在界面上"]}
```
'''
        
        extracted_replies = gui._extract_quick_replies_from_content(invalid_response)
        valid_replies = gui._validate_quick_reply_options(extracted_replies)
        
        expected_valid = ["询问修炼方法"]
        
        if valid_replies == expected_valid:
            print("✓ 无效选项过滤成功")
            print(f"  过滤后结果: {valid_replies}")
        else:
            print("✗ 无效选项过滤失败")
            print(f"  期望: {expected_valid}")
            print(f"  实际: {valid_replies}")
            return False
        
        # 5. 测试向后兼容性
        print("\n5. 测试向后兼容性...")
        
        # 模拟旧格式的JSON数组
        old_format_response = '''
这里是一些快捷回复选项：

```json
["选择进入修仙门派", "询问修炼功法", "查看个人状态", "继续探索"]
```
'''
        
        extracted_replies = gui._extract_quick_replies_from_content(old_format_response)
        
        expected_old = ["选择进入修仙门派", "询问修炼功法", "查看个人状态", "继续探索"]
        
        if extracted_replies == expected_old:
            print("✓ 向后兼容性测试成功")
            print(f"  提取结果: {extracted_replies}")
        else:
            print("✗ 向后兼容性测试失败")
            print(f"  期望: {expected_old}")
            print(f"  实际: {extracted_replies}")
            return False
        
        # 6. 测试边界情况
        print("\n6. 测试边界情况...")
        
        # 测试空内容
        empty_result = gui._extract_quick_replies_from_content("")
        if empty_result == []:
            print("✓ 空内容处理正确")
        else:
            print("✗ 空内容处理错误")
            return False
        
        # 测试无JSON内容
        no_json_result = gui._extract_quick_replies_from_content("这里没有任何JSON内容")
        if no_json_result == []:
            print("✓ 无JSON内容处理正确")
        else:
            print("✗ 无JSON内容处理错误")
            return False
        
        # 测试损坏的JSON
        broken_json_response = '''
```json
{"quick_replies":["选项1", "选项2", "选项3"
```
'''
        broken_result = gui._extract_quick_replies_from_content(broken_json_response)
        if broken_result == []:
            print("✓ 损坏JSON处理正确")
        else:
            print("✗ 损坏JSON处理错误")
            return False
        
        # 7. 测试多行JSON解析
        print("\n7. 测试多行JSON解析...")
        
        multiline_response = '''
这是多行JSON格式：

```json
{
    "quick_replies": [
        "询问修炼心得",
        "请教功法要点",
        "了解门派规矩"
    ]
}
```
'''
        
        multiline_result = gui._extract_quick_replies_from_content(multiline_response)
        expected_multiline = ["询问修炼心得", "请教功法要点", "了解门派规矩"]
        
        if multiline_result == expected_multiline:
            print("✓ 多行JSON解析成功")
            print(f"  提取结果: {multiline_result}")
        else:
            print("✗ 多行JSON解析失败")
            print(f"  期望: {expected_multiline}")
            print(f"  实际: {multiline_result}")
            return False
        
        # 8. 测试完整的解析流程
        print("\n8. 测试完整的解析流程...")
        
        # 模拟完整的AI响应
        full_response = '''
〖第一章1/100〗第1回，〖青云山门〗青云山坐落在云雾缭绕的山峰之上，山门巍峨壮观。

{"触发独处聊天程序":["苏瑶", "正道/太清宗/内门核心弟子 筑基九层半步金丹", "苏瑶100", "张轩0", "王虎0"]}

【张三】修为筑基初期，正在山门前犹豫不决。

「内心活动」这里就是传说中的修仙门派吗？我真的能在这里学到仙法吗？

```json
{"quick_replies":["鼓起勇气走向山门", "观察周围环境", "询问门派弟子", "暂时离开重新考虑"]}
```

「系统提示」任务进度：寻找修仙门派 1/1 完成。
'''
        
        # 使用完整的解析方法
        final_result = gui._extract_quick_replies_from_content(full_response)
        final_valid = gui._validate_quick_reply_options(final_result)
        
        expected_final = ["鼓起勇气走向山门", "观察周围环境", "询问门派弟子", "暂时离开重新考虑"]
        
        if final_valid == expected_final:
            print("✓ 完整解析流程成功")
            print(f"  最终结果: {final_valid}")
        else:
            print("✗ 完整解析流程失败")
            print(f"  期望: {expected_final}")
            print(f"  实际: {final_valid}")
            return False
        
        # 清理
        gui.root.destroy()
        
        print("\n" + "=" * 80)
        print("🎉 所有快捷回复JSON解析修复测试通过！")
        print("=" * 80)
        
        print("\n📊 测试结果总结:")
        print("  ✅ 多JSON对象解析: 正确")
        print("  ✅ 无效选项过滤: 正确")
        print("  ✅ 向后兼容性: 正确")
        print("  ✅ 边界情况处理: 正确")
        print("  ✅ 多行JSON解析: 正确")
        print("  ✅ 完整解析流程: 正确")
        
        print("\n💡 修复功能特性:")
        print("  🎯 智能识别: 优先识别 {\"quick_replies\": [...]} 格式")
        print("  🛡️ 错误容忍: 处理多JSON对象和损坏的JSON")
        print("  🔍 内容验证: 过滤角色名+数字等无效选项")
        print("  🔄 向后兼容: 继续支持旧的JSON数组格式")
        print("  📏 长度限制: 自动限制选项数量和长度")
        
        print("\n🎮 用户体验改进:")
        print("  1. 正确显示有意义的对话选项")
        print("  2. 自动过滤无关的剧情数据")
        print("  3. 处理复杂的AI响应格式")
        print("  4. 提供稳定可靠的快捷回复功能")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = test_json_parsing_fixes()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
