#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试豆包1.6深度思考模型的思考内容显示
"""

import sys
import tkinter as tk
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI


def create_doubao_thinking_test_gui():
    """创建豆包思考内容测试GUI"""
    print("创建豆包1.6深度思考模型测试GUI...")
    
    # 创建主窗口
    app = ChatGUI()
    
    # 创建测试响应，包含触发关键聊天剧情的内容
    test_response = '''
〖豆包思考测试〗第1回〖测试场景〗这是一个专门用于测试豆包1.6深度思考模型思考内容显示的场景。

【豆包测试NPC】（测试身份/测试修为）
豆包测试NPC看着你说道："这是一个测试对话，用于验证豆包1.6模型的思考内容是否能正确显示。"

```json
{"触发关键聊天剧情":["豆包测试NPC", "测试身份/测试修为","你是豆包测试NPC，专门用于测试豆包1.6深度思考模型的思考内容显示功能。请在回复中包含深度思考过程。","你与玩家是测试关系，用于验证豆包模型功能。","你性格直接，专门用于测试豆包思考内容显示。","当前在测试场景中，需要验证豆包模型的思考内容显示效果。","3回合"]}
```

```json
{"quick_replies":["测试豆包思考", "验证深度思考", "继续测试"]}
```
    '''
    
    # 添加测试控制面板
    test_frame = tk.Frame(app.main_frame)
    test_frame.pack(fill=tk.X, pady=10)
    
    # 标题
    title_label = tk.Label(
        test_frame,
        text="🧠 豆包1.6深度思考模型测试",
        font=("Microsoft YaHei", 14, "bold"),
        fg="#e74c3c"
    )
    title_label.pack(pady=5)
    
    # 说明文字
    info_text = """
🎯 测试目标：验证豆包1.6深度思考模型的思考内容正确显示

📋 支持的豆包模型：
• doubao-seed-1-6-250615 (豆包种子1.6标准版)
• doubao-seed-1-6-thinking-250715 (豆包种子1.6思考版)
• doubao-seed-1-6-flash-250715 (豆包种子1.6闪电版)

🔧 技术特点：
• 自适应思考模式：使用"auto"模式让模型自动判断是否需要深度思考
• thinking参数支持：在API请求中传入thinking参数控制思考开关
• 思考内容解析：正确解析thinking_content字段并显示

🔍 验证要点：
- 思考内容应以灰色斜体文字显示
- 思考内容应在正文内容之前显示
- 思考内容不应保存到历史记录中
- 所有场景下的思考内容都应正确显示

⚠️ 注意事项：
- 请确保当前模型设置为豆包1.6系列模型
- 确保火山引擎API密钥已正确配置
- 观察思考内容的颜色和字体样式
    """
    
    info_label = tk.Label(
        test_frame,
        text=info_text,
        font=("Microsoft YaHei", 9),
        fg="#2c3e50",
        justify=tk.LEFT
    )
    info_label.pack(pady=5)
    
    # 模型选择区域
    model_frame = tk.Frame(test_frame)
    model_frame.pack(fill=tk.X, pady=5)
    
    tk.Label(model_frame, text="选择豆包模型:", font=("Microsoft YaHei", 10, "bold")).pack(side=tk.LEFT)
    
    doubao_models = [
        "doubao-seed-1-6-250615",
        "doubao-seed-1-6-thinking-250715", 
        "doubao-seed-1-6-flash-250715"
    ]
    
    def switch_to_doubao_model(model):
        app.current_model.set(model)
        status_var.set(f"已切换到模型: {model}")
        log_step(f"🔄 切换到豆包模型: {model}")
    
    for model in doubao_models:
        btn = tk.Button(
            model_frame,
            text=model.split('-')[-1],  # 显示简化名称
            command=lambda m=model: switch_to_doubao_model(m),
            bg="#3498db",
            fg="white",
            font=("Microsoft YaHei", 9),
            padx=10,
            pady=2
        )
        btn.pack(side=tk.LEFT, padx=2)
    
    # 测试状态显示
    status_frame = tk.Frame(test_frame)
    status_frame.pack(fill=tk.X, pady=5)
    
    status_var = tk.StringVar(value="等待开始测试...")
    status_label = tk.Label(
        status_frame,
        textvariable=status_var,
        font=("Microsoft YaHei", 10, "bold"),
        fg="#27ae60"
    )
    status_label.pack()
    
    # 测试步骤跟踪
    steps_frame = tk.Frame(test_frame)
    steps_frame.pack(fill=tk.X, pady=5)
    
    steps_text = tk.Text(
        steps_frame,
        height=10,
        width=80,
        font=("Consolas", 9),
        bg="#f8f9fa",
        fg="#2c3e50"
    )
    steps_text.pack(fill=tk.BOTH, expand=True)
    
    def log_step(message):
        """记录测试步骤"""
        timestamp = time.strftime("%H:%M:%S")
        steps_text.insert(tk.END, f"[{timestamp}] {message}\n")
        steps_text.see(tk.END)
        steps_text.update()
    
    def start_doubao_thinking_test():
        """开始豆包思考内容测试"""
        log_step("🧠 开始豆包1.6深度思考模型测试")
        status_var.set("测试进行中...")
        
        # 检查当前模型
        current_model = app.current_model.get()
        if current_model not in doubao_models:
            log_step(f"⚠️  当前模型: {current_model}，建议切换到豆包1.6系列模型")
            log_step("   请点击上方按钮切换到豆包模型")
            status_var.set("请先切换到豆包模型")
            return
        else:
            log_step(f"✅ 当前模型: {current_model}")
        
        # 清空之前的消息
        app.message_text.config(state=tk.NORMAL)
        app.message_text.delete(1.0, tk.END)
        app.message_text.config(state=tk.DISABLED)
        
        log_step("📤 模拟接收AI响应（包含触发关键聊天剧情）...")
        # 模拟接收到AI响应
        app._display_message("assistant", test_response)
        app.history_manager.add_message("assistant", test_response)
        
        log_step("🔍 触发对话检测...")
        # 解析并显示（这会触发对话窗口）
        result = app._check_dialogue_trigger(test_response)
        
        if result:
            log_step("✅ 对话触发成功，对话窗口已创建")
            log_step("👀 请观察以下内容：")
            log_step("   1. 对话窗口中NPC自动开场时的思考内容显示")
            log_step("   2. 与NPC对话时的思考内容显示")
            log_step("   3. 对话结束后主线剧情衔接时的思考内容显示")
            log_step("   4. 思考内容应为灰色斜体文字")
            log_step("   5. 观察thinking参数是否正确传递")
            status_var.set("对话窗口已创建，请观察豆包思考内容显示...")
        else:
            log_step("❌ 对话触发失败")
            status_var.set("测试失败：对话触发失败")
    
    def test_doubao_main_chat():
        """测试主聊天窗口的豆包思考内容显示"""
        log_step("🧠 测试主聊天窗口豆包思考内容显示")
        
        current_model = app.current_model.get()
        if current_model not in doubao_models:
            log_step(f"⚠️  当前模型: {current_model}，请先切换到豆包模型")
            return
        
        status_var.set("测试主聊天窗口...")
        
        # 发送一个需要深度思考的测试消息
        test_message = "请用豆包1.6深度思考模型回复这条消息：解释一下量子计算的基本原理，并分析其在未来可能的应用场景。请在回复中包含你的思考过程。"
        
        # 模拟用户输入
        app.input_text.delete(1.0, tk.END)
        app.input_text.insert(1.0, test_message)
        
        log_step(f"📤 发送深度思考测试消息")
        log_step("👀 请观察主聊天窗口中的思考内容显示效果")
        log_step("   - 思考内容应以灰色斜体显示")
        log_step("   - thinking参数应设置为'auto'")
        
        # 触发发送
        app._send_message()
        
        status_var.set("主聊天测试进行中，请观察豆包思考内容...")
    
    def reset_test():
        """重置测试"""
        log_step("🔄 重置测试环境")
        status_var.set("等待开始测试...")
        steps_text.delete(1.0, tk.END)
        
        # 清空聊天记录
        app.message_text.config(state=tk.NORMAL)
        app.message_text.delete(1.0, tk.END)
        app.message_text.config(state=tk.DISABLED)
    
    # 测试按钮
    button_frame = tk.Frame(test_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    start_button = tk.Button(
        button_frame,
        text="🚀 开始对话窗口测试",
        command=start_doubao_thinking_test,
        bg="#3498db",
        fg="white",
        font=("Microsoft YaHei", 11, "bold"),
        padx=20,
        pady=5
    )
    start_button.pack(side=tk.LEFT, padx=5)
    
    main_chat_button = tk.Button(
        button_frame,
        text="🧠 测试主聊天思考内容",
        command=test_doubao_main_chat,
        bg="#9b59b6",
        fg="white",
        font=("Microsoft YaHei", 11, "bold"),
        padx=20,
        pady=5
    )
    main_chat_button.pack(side=tk.LEFT, padx=5)
    
    reset_button = tk.Button(
        button_frame,
        text="🔄 重置测试",
        command=reset_test,
        bg="#95a5a6",
        fg="white",
        font=("Microsoft YaHei", 11, "bold"),
        padx=20,
        pady=5
    )
    reset_button.pack(side=tk.LEFT, padx=5)
    
    print("豆包思考内容测试GUI创建完成")
    print("=" * 80)
    print("🧠 豆包1.6深度思考模型测试")
    print("=" * 80)
    print("本测试将验证以下功能：")
    print("1. 豆包模型thinking参数的正确传递")
    print("2. thinking_content字段的正确解析")
    print("3. 思考内容在各种场景下的正确显示")
    print("4. auto模式的自适应思考功能")
    print()
    print("请在GUI中选择豆包模型并点击相应按钮开始测试！")
    print("=" * 80)
    
    return app


def main():
    """主函数"""
    print("🧠 启动豆包1.6深度思考模型测试程序")
    print("=" * 100)
    
    try:
        # 创建测试GUI
        app = create_doubao_thinking_test_gui()
        
        # 运行GUI主循环
        app.run()
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
        return 0
    except Exception as e:
        print(f"💥 测试程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("测试完成")
    return 0


if __name__ == "__main__":
    sys.exit(main())
