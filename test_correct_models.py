#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用正确的模型名称测试火山引擎API
基于官方文档的正确模型名称格式
"""

import os
import sys
import json
import requests
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_environment():
    """加载环境变量"""
    try:
        from dotenv import load_dotenv
        env_path = project_root / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            return True
        else:
            print(f"⚠ 环境变量文件不存在: {env_path}")
            return False
    except ImportError:
        print("⚠ python-dotenv 未安装")
        return True

def test_correct_model_names():
    """使用正确的模型名称进行测试"""
    print("=" * 60)
    print("使用正确模型名称测试火山引擎API")
    print("=" * 60)
    
    # 加载环境变量
    load_environment()
    api_key = os.getenv('volcengine_API_KEY')
    
    if not api_key:
        print("✗ 未找到API密钥")
        return False
    
    print(f"✓ 已获取API密钥: {api_key[:10]}...")
    
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 基于官方文档的正确模型名称
    correct_models = [
        {
            "id": "doubao-seed-1.6-flash",
            "name": "豆包种子1.6闪电版",
            "description": "响应速度快，适合实时对话",
            "user_opened": True
        },
        {
            "id": "doubao-seed-1.6-thinking",
            "name": "豆包种子1.6思考版", 
            "description": "具备深度推理能力，适合复杂任务",
            "user_opened": True
        },
        {
            "id": "doubao-seed-1.6",
            "name": "豆包种子1.6标准版",
            "description": "平衡性能和成本",
            "user_opened": True
        },
        {
            "id": "doubao-1-5-pro-32k-250115",
            "name": "豆包1.5专业版32K",
            "description": "支持32K上下文长度",
            "user_opened": False
        },
        {
            "id": "doubao-1-5-lite-32k-250115", 
            "name": "豆包1.5轻量版32K",
            "description": "轻量级长文本模型",
            "user_opened": False
        }
    ]
    
    successful_models = []
    failed_models = []
    
    print(f"\n开始测试 {len(correct_models)} 个模型...\n")
    
    for i, model in enumerate(correct_models, 1):
        status_icon = "🔥" if model["user_opened"] else "📋"
        status_text = "用户已开通" if model["user_opened"] else "标准模型"
        
        print(f"[{i}/{len(correct_models)}] {status_icon} 测试模型: {model['id']}")
        print(f"    名称: {model['name']} ({status_text})")
        print(f"    描述: {model['description']}")
        
        test_data = {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "你好，请简单介绍一下自己。"}
            ],
            "model": model['id'],
            "max_tokens": 100,
            "temperature": 0.7,
            "stream": False
        }
        
        try:
            response = requests.post(url, headers=headers, json=test_data, timeout=20)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    usage = result.get('usage', {})
                    
                    print(f"    ✅ 成功！")
                    print(f"    响应: {content[:80]}{'...' if len(content) > 80 else ''}")
                    print(f"    Token: 输入={usage.get('prompt_tokens', 0)}, 输出={usage.get('completion_tokens', 0)}, 总计={usage.get('total_tokens', 0)}")
                    
                    successful_models.append({
                        'model': model,
                        'response': content,
                        'usage': usage
                    })
                else:
                    print(f"    ❌ 响应格式异常")
                    failed_models.append({'model': model, 'error': '响应格式异常'})
            else:
                try:
                    error_info = response.json()
                    error_msg = error_info.get('error', {}).get('message', '未知错误')
                    print(f"    ❌ 失败 (状态码: {response.status_code})")
                    print(f"    错误: {error_msg[:100]}{'...' if len(error_msg) > 100 else ''}")
                    failed_models.append({'model': model, 'error': error_msg})
                except:
                    print(f"    ❌ 失败 (状态码: {response.status_code})")
                    failed_models.append({'model': model, 'error': f'HTTP {response.status_code}'})
                    
        except requests.exceptions.Timeout:
            print(f"    ❌ 请求超时")
            failed_models.append({'model': model, 'error': '请求超时'})
        except requests.exceptions.ConnectionError:
            print(f"    ❌ 连接错误")
            failed_models.append({'model': model, 'error': '连接错误'})
        except Exception as e:
            print(f"    ❌ 异常: {str(e)}")
            failed_models.append({'model': model, 'error': str(e)})
        
        print()  # 空行分隔
        time.sleep(1)  # 避免请求过于频繁
    
    # 显示详细测试结果
    print("=" * 60)
    print("详细测试结果")
    print("=" * 60)
    
    print(f"\n🎉 成功的模型 ({len(successful_models)} 个):")
    if successful_models:
        for item in successful_models:
            model = item['model']
            usage = item['usage']
            print(f"\n✅ {model['id']}")
            print(f"   名称: {model['name']}")
            print(f"   用户状态: {'已开通' if model['user_opened'] else '标准模型'}")
            print(f"   响应: {item['response'][:100]}{'...' if len(item['response']) > 100 else ''}")
            print(f"   Token使用: 输入={usage.get('prompt_tokens', 0)}, 输出={usage.get('completion_tokens', 0)}")
    else:
        print("   无成功的模型")
    
    print(f"\n❌ 失败的模型 ({len(failed_models)} 个):")
    if failed_models:
        for item in failed_models:
            model = item['model']
            print(f"\n❌ {model['id']}")
            print(f"   名称: {model['name']}")
            print(f"   用户状态: {'已开通' if model['user_opened'] else '标准模型'}")
            print(f"   错误: {item['error'][:100]}{'...' if len(item['error']) > 100 else ''}")
    else:
        print("   无失败的模型")
    
    # 提供具体建议
    print(f"\n" + "=" * 60)
    print("结论和建议")
    print("=" * 60)
    
    if successful_models:
        print("\n🎯 可用模型:")
        for item in successful_models:
            model = item['model']
            print(f"  - {model['id']} ({model['name']})")
        
        print("\n📋 集成建议:")
        print("1. 优先使用测试成功的模型进行开发")
        print("2. 在聊天客户端中添加这些模型作为选项")
        print("3. 实现模型自动切换和降级机制")
    
    if failed_models:
        user_opened_failed = [item for item in failed_models if item['model']['user_opened']]
        if user_opened_failed:
            print(f"\n⚠️ 用户已开通但无法访问的模型 ({len(user_opened_failed)} 个):")
            for item in user_opened_failed:
                print(f"  - {item['model']['id']}")
            print("\n建议:")
            print("1. 检查模型是否需要额外的激活步骤")
            print("2. 联系火山引擎技术支持确认访问权限")
            print("3. 确认API密钥是否有访问这些模型的权限")
    
    print(f"\n💡 总体建议:")
    print("1. 使用测试成功的模型开始集成工作")
    print("2. 对于失败的模型，可以稍后再次测试或联系技术支持")
    print("3. 实现动态模型检测，自动识别可用模型")
    
    return len(successful_models) > 0

def main():
    """主函数"""
    try:
        success = test_correct_model_names()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
