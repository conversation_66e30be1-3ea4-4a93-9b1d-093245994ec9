#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试DeepSeek-V3-0324通用对话模型（价格8）的API请求
重现和解决400错误问题
"""

import sys
import json
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_deepseek_chat_basic():
    """测试deepseek-chat模型的基础请求"""
    print("=" * 60)
    print("1. 测试deepseek-chat模型基础请求")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.config import config
        
        api_key = config.get_api_key()
        base_url = config.get_app_setting("api.base_url", "https://api.deepseek.com")
        
        # 构建请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 测试不同的参数组合
        test_cases = [
            {
                "name": "最简单请求",
                "data": {
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": "你好"}],
                    "stream": False
                }
            },
            {
                "name": "带max_tokens的请求",
                "data": {
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": "你好"}],
                    "stream": False,
                    "max_tokens": 100
                }
            },
            {
                "name": "带temperature的请求",
                "data": {
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": "你好"}],
                    "stream": False,
                    "max_tokens": 100,
                    "temperature": 0.7
                }
            },
            {
                "name": "流式请求",
                "data": {
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": "你好"}],
                    "stream": True,
                    "max_tokens": 100,
                    "temperature": 0.7
                }
            },
            {
                "name": "大max_tokens请求",
                "data": {
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": "请详细介绍一下人工智能的发展历史"}],
                    "stream": True,
                    "max_tokens": 65536,
                    "temperature": 0.7
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['name']}")
            print(f"请求数据: {json.dumps(test_case['data'], ensure_ascii=False, indent=2)}")
            
            try:
                response = requests.post(
                    f"{base_url}/chat/completions",
                    headers=headers,
                    json=test_case['data'],
                    timeout=30
                )
                
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 请求成功")
                    if test_case['data']['stream']:
                        # 处理流式响应
                        chunk_count = 0
                        for line in response.iter_lines():
                            if line:
                                chunk_count += 1
                                if chunk_count > 5:  # 只处理前5个chunk
                                    break
                        print(f"  处理了 {chunk_count} 个数据块")
                    else:
                        # 处理非流式响应
                        response_data = response.json()
                        content = response_data['choices'][0]['message']['content']
                        print(f"  响应内容: {content[:50]}...")
                else:
                    print(f"❌ 请求失败: {response.status_code}")
                    print(f"  错误响应: {response.text}")
                    
                    # 分析错误原因
                    try:
                        error_data = response.json()
                        if 'error' in error_data:
                            error_info = error_data['error']
                            print(f"  错误类型: {error_info.get('type', 'unknown')}")
                            print(f"  错误消息: {error_info.get('message', 'unknown')}")
                            print(f"  错误代码: {error_info.get('code', 'unknown')}")
                    except:
                        pass
                        
            except Exception as e:
                print(f"❌ 请求异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_client_deepseek_chat():
    """测试客户端对deepseek-chat模型的调用"""
    print("\n" + "=" * 60)
    print("2. 测试客户端deepseek-chat模型调用")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.api_client import DeepSeekAPIClient, ChatMessage
        
        # 创建API客户端
        client = DeepSeekAPIClient()
        print("✅ API客户端创建成功")
        
        # 测试不同的参数组合
        test_cases = [
            {
                "name": "默认参数",
                "params": {
                    "model": "deepseek-chat",
                    "max_tokens": 100,
                    "temperature": 0.7,
                    "stream": True
                }
            },
            {
                "name": "高温度参数",
                "params": {
                    "model": "deepseek-chat",
                    "max_tokens": 200,
                    "temperature": 1.5,
                    "stream": True
                }
            },
            {
                "name": "大Token数量",
                "params": {
                    "model": "deepseek-chat",
                    "max_tokens": 8192,
                    "temperature": 0.7,
                    "stream": True
                }
            },
            {
                "name": "最大Token数量",
                "params": {
                    "model": "deepseek-chat",
                    "max_tokens": 65536,
                    "temperature": 0.7,
                    "stream": True
                }
            }
        ]
        
        messages = [ChatMessage(role="user", content="你好，请简单介绍一下你自己")]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['name']}")
            print(f"参数: {test_case['params']}")
            
            try:
                response_content = ""
                chunk_count = 0
                error_occurred = False
                
                for chunk in client.chat_completion(
                    messages=messages,
                    **test_case['params']
                ):
                    chunk_count += 1
                    
                    if chunk.error:
                        print(f"❌ 客户端错误: {chunk.error}")
                        error_occurred = True
                        break
                    
                    if chunk.is_complete:
                        break
                    
                    if chunk.content:
                        response_content += chunk.content
                
                if not error_occurred:
                    print(f"✅ 测试成功")
                    print(f"  处理了 {chunk_count} 个数据块")
                    print(f"  响应内容: {response_content[:50]}...")
                    
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_client_deepseek_chat():
    """测试统一客户端对deepseek-chat模型的调用"""
    print("\n" + "=" * 60)
    print("3. 测试统一客户端deepseek-chat模型调用")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        from deepseek_chat_client.api_client import ChatMessage
        
        # 创建统一客户端
        client = UnifiedAPIClient()
        print("✅ 统一客户端创建成功")
        
        # 获取deepseek-chat模型信息
        model_info = client.get_model_info("deepseek-chat")
        print(f"模型信息: {model_info}")
        
        # 测试消息
        messages = [ChatMessage(role="user", content="你好，这是一个测试消息")]
        
        print("\n发送测试消息...")
        
        try:
            response_content = ""
            chunk_count = 0
            
            for chunk in client.chat_completion(
                messages=messages,
                model="deepseek-chat",
                max_tokens=100,
                temperature=0.7,
                stream=True
            ):
                chunk_count += 1
                
                if chunk.error:
                    print(f"❌ 统一客户端错误: {chunk.error}")
                    return False
                
                if chunk.is_complete:
                    break
                
                if chunk.content:
                    response_content += chunk.content
            
            print(f"✅ 统一客户端测试成功")
            print(f"处理了 {chunk_count} 个数据块")
            print(f"响应内容: {response_content[:50]}...")
            return True
            
        except Exception as e:
            print(f"❌ 统一客户端调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 统一客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_potential_issues():
    """分析deepseek-chat模型可能的问题"""
    print("\n" + "=" * 60)
    print("4. deepseek-chat模型问题分析")
    print("=" * 60)
    
    print("可能导致400错误的原因:")
    print("\n1. 参数限制问题:")
    print("   - deepseek-chat模型可能有特定的max_tokens限制")
    print("   - temperature参数范围可能与deepseek-reasoner不同")
    print("   - 某些参数组合可能不被支持")
    
    print("\n2. 请求格式问题:")
    print("   - messages格式可能有特殊要求")
    print("   - 系统消息处理方式可能不同")
    print("   - 流式请求参数可能有差异")
    
    print("\n3. 内容限制问题:")
    print("   - 消息内容长度限制")
    print("   - 特殊字符或格式限制")
    print("   - 编码问题")
    
    print("\n4. API版本问题:")
    print("   - deepseek-chat可能使用不同的API版本")
    print("   - 某些新参数可能不被支持")
    print("   - 兼容性问题")

def main():
    """主函数"""
    print("DeepSeek-V3-0324通用对话模型（价格8）测试工具")
    print("专门用于重现和解决400错误问题")
    
    # 运行所有测试
    tests = [
        test_deepseek_chat_basic,
        test_client_deepseek_chat,
        test_unified_client_deepseek_chat
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试执行失败: {e}")
            results.append(False)
    
    # 显示分析结果
    analyze_potential_issues()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"测试通过: {passed}/{total}")
    
    if passed == total:
        print("✅ deepseek-chat模型测试全部通过")
    else:
        print("❌ 部分测试失败，发现问题")
        print("\n建议的修复步骤:")
        print("1. 检查具体的错误信息")
        print("2. 调整参数配置")
        print("3. 验证请求格式")
        print("4. 检查API文档更新")

if __name__ == "__main__":
    main()
