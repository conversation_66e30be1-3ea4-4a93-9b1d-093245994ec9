#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按钮宽度修复效果
验证ttk.Button的width参数使用字符宽度而不是像素宽度
"""

import sys
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_button_width_calculation():
    """测试按钮宽度计算方法"""
    print("=" * 60)
    print("1. 测试按钮宽度计算方法")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏主窗口
        
        # 测试不同的文本
        test_texts = [
            "恭敬行礼: 陛下愿听长老指点",
            "营房后退: 如何证明您身份？",
            "直接询问: 三道试炼是什么？",
            "展示玉佩: 此物实众发光是何缘由？",
            "是",
            "好的",
            "询问师姐修炼心得"
        ]
        
        print("文本宽度计算测试:")
        for text in test_texts:
            # 计算文本显示宽度
            text_width = gui._calculate_text_width(text)
            
            # 计算截断后的文本
            truncated = gui._truncate_text(text, gui.button_max_text_length)
            
            # 计算按钮字符宽度
            button_width = gui._calculate_button_width_chars(truncated)
            
            print(f"原文: '{text}'")
            print(f"  文本宽度: {text_width}")
            print(f"  截断文本: '{truncated}'")
            print(f"  按钮字符宽度: {button_width}")
            print()
        
        gui.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 按钮宽度计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_button_display():
    """测试实际按钮显示效果"""
    print("\n" + "=" * 60)
    print("2. 测试实际按钮显示效果")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.title("按钮宽度修复测试")
        
        # 使用图片中的实际文本进行测试
        test_options = [
            "恭敬行礼: 陛下愿听长老指点",
            "营房后退: 如何证明您身份？",
            "直接询问: 三道试炼是什么？",
            "展示玉佩: 此物实众发光是何缘由？"
        ]
        
        print("显示测试按钮...")
        print(f"测试选项: {test_options}")
        
        # 显示快捷回复按钮
        gui._show_quick_reply_buttons(test_options)
        
        # 更新状态栏
        gui.status_var.set("按钮宽度修复测试 - 请检查按钮是否显示完整文本")
        
        # 在消息区域显示说明
        gui.message_text.config(state=tk.NORMAL)
        test_info = """
🔧 按钮宽度修复测试

修复内容:
✅ 修正了ttk.Button的width参数使用方式
✅ 使用字符宽度而不是像素宽度
✅ 改进了按钮宽度计算算法

测试内容:
• 恭敬行礼: 陛下愿听长老指点
• 营房后退: 如何证明您身份？
• 直接询问: 三道试炼是什么？
• 展示玉佩: 此物实众发光是何缘由？

请检查:
1. 按钮是否显示完整的文本内容
2. 按钮宽度是否合适
3. 工具提示是否正常工作
4. 按钮布局是否整齐

如果文本仍然被截断，请将鼠标悬停在按钮上查看工具提示。
"""
        gui.message_text.insert(tk.END, test_info, "system")
        gui.message_text.config(state=tk.DISABLED)
        gui.message_text.see(tk.END)
        
        print("\n请在GUI窗口中检查:")
        print("1. 按钮是否显示完整的文本内容")
        print("2. 按钮宽度是否合适")
        print("3. 工具提示是否正常工作")
        print("4. 按钮布局是否整齐")
        
        # 运行GUI
        gui.run()
        
        return True
        
    except Exception as e:
        print(f"❌ 实际按钮显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_width_parameter_understanding():
    """测试对ttk.Button width参数的理解"""
    print("\n" + "=" * 60)
    print("3. ttk.Button width参数测试")
    print("=" * 60)
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("ttk.Button width参数测试")
        root.geometry("800x400")
        
        # 创建说明标签
        info_label = tk.Label(
            root,
            text="ttk.Button的width参数是以字符为单位，不是像素",
            font=("Arial", 12),
            fg="red"
        )
        info_label.pack(pady=10)
        
        # 测试不同的width值
        test_cases = [
            {"text": "短文本", "width": 10},
            {"text": "中等长度的文本内容", "width": 20},
            {"text": "这是一个很长的文本内容，用来测试按钮宽度", "width": 30},
            {"text": "恭敬行礼: 陛下愿听长老指点", "width": 25},
            {"text": "营房后退: 如何证明您身份？", "width": 25}
        ]
        
        for i, case in enumerate(test_cases):
            frame = tk.Frame(root)
            frame.pack(pady=5, fill=tk.X, padx=20)
            
            # 标签说明
            label = tk.Label(
                frame,
                text=f"文本: '{case['text']}', width={case['width']}",
                font=("Arial", 9)
            )
            label.pack(side=tk.LEFT)
            
            # 按钮
            btn = tk.Button(
                frame,
                text=case['text'],
                width=case['width']
            )
            btn.pack(side=tk.RIGHT)
            
            print(f"{i+1}. 文本: '{case['text']}', width={case['width']}")
        
        print("\n请观察不同width值对按钮显示的影响")
        print("关闭窗口继续测试...")
        
        # 运行测试窗口
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ width参数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("按钮宽度修复测试")
    print("解决ttk.Button width参数使用问题")
    
    # 运行计算测试
    calc_result = test_button_width_calculation()
    
    # 询问是否运行交互式测试
    try:
        response = input("\n是否运行交互式测试？(y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            # 运行width参数理解测试
            width_result = test_width_parameter_understanding()
            
            # 运行实际按钮显示测试
            display_result = test_actual_button_display()
            
            results = [calc_result, width_result, display_result]
        else:
            results = [calc_result]
    except KeyboardInterrupt:
        print("\n交互式测试已取消")
        results = [calc_result]
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"测试通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 按钮宽度修复测试通过")
        print("\n修复内容:")
        print("1. ✅ 修正了ttk.Button的width参数使用方式")
        print("2. ✅ 使用字符宽度而不是像素宽度")
        print("3. ✅ 改进了按钮宽度计算算法")
        print("4. ✅ 确保按钮能显示完整文本内容")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
