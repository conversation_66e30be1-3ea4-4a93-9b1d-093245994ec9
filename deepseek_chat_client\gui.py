# -*- coding: utf-8 -*-
"""
GUI界面模块
使用Tkinter创建类似ChatGPT的聊天界面
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import json
import re
import time
import random
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
from pathlib import Path


class ToolTip:
    """工具提示类，用于在鼠标悬停时显示完整文本"""

    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        self.widget.bind("<Enter>", self.on_enter)
        self.widget.bind("<Leave>", self.on_leave)
        self.widget.bind("<Motion>", self.on_motion)

    def on_enter(self, event=None):
        """鼠标进入时显示工具提示"""
        if self.text and len(self.text) > 20:  # 只有长文本才显示工具提示
            self.show_tooltip()

    def on_leave(self, event=None):
        """鼠标离开时隐藏工具提示"""
        self.hide_tooltip()

    def on_motion(self, event=None):
        """鼠标移动时更新工具提示位置"""
        if self.tooltip_window:
            self.update_tooltip_position(event)

    def show_tooltip(self):
        """显示工具提示"""
        if self.tooltip_window:
            return

        x = self.widget.winfo_rootx() + 25
        y = self.widget.winfo_rooty() + 25

        self.tooltip_window = tk.Toplevel(self.widget)
        self.tooltip_window.wm_overrideredirect(True)
        self.tooltip_window.wm_geometry(f"+{x}+{y}")

        # 创建工具提示标签
        label = tk.Label(
            self.tooltip_window,
            text=self.text,
            background="#ffffe0",
            foreground="#000000",
            relief="solid",
            borderwidth=1,
            font=("Arial", 9),
            wraplength=300,
            justify="left",
            padx=5,
            pady=3
        )
        label.pack()

    def hide_tooltip(self):
        """隐藏工具提示"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None

    def update_tooltip_position(self, event):
        """更新工具提示位置"""
        if self.tooltip_window:
            x = self.widget.winfo_rootx() + 25
            y = self.widget.winfo_rooty() + 25
            self.tooltip_window.wm_geometry(f"+{x}+{y}")


try:
    # Try relative imports first (when run as module)
    from .config import config
    from .api_client import DeepSeekAPIClient, ChatMessage, StreamChunk
    from .unified_client import UnifiedAPIClient
    from .chat_history import ChatHistoryManager
    from .dialogue_window import DialogueWindow
except ImportError:
    # Fall back to absolute imports (when run directly)
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from deepseek_chat_client.config import config
    from deepseek_chat_client.api_client import DeepSeekAPIClient, ChatMessage, StreamChunk
    from deepseek_chat_client.unified_client import UnifiedAPIClient
    from deepseek_chat_client.chat_history import ChatHistoryManager
    from deepseek_chat_client.dialogue_window import DialogueWindow


class ChatGUI:
    """聊天GUI主类"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.api_client = UnifiedAPIClient()
        self.history_manager = ChatHistoryManager()
        
        # GUI配置
        self.window_width = config.get_app_setting("window.width", 1440)
        self.window_height = config.get_app_setting("window.height", 700)
        self.font_family = config.get_app_setting("ui.font_family", "Microsoft YaHei")
        self.font_size = config.get_app_setting("ui.font_size", 10)
        
        # 颜色配置
        self.thinking_color = config.get_app_setting("ui.thinking_color", "#666666")
        self.user_color = config.get_app_setting("ui.user_message_color", "#0066cc")
        self.assistant_color = config.get_app_setting("ui.assistant_message_color", "#333333")
        
        # 状态变量
        self.is_sending = False
        self.current_model = tk.StringVar(value=config.get_app_setting("api.default_model", "deepseek-reasoner"))
        self.max_tokens = tk.IntVar(value=config.get_app_setting("api.default_max_tokens", 65536))
        self.temperature = tk.DoubleVar(value=config.get_app_setting("api.default_temperature", 0.7))
        self.current_system_prompt_file = tk.StringVar(value="")
        self.current_system_prompt_content = ""
        self.cached_plot_output_content = ""  # 缓存的剧情必要输出内容

        # 快捷回复相关变量
        self.quick_reply_buttons = []
        self.auto_call_function_calling = tk.BooleanVar(
            value=config.get_app_setting("ui.quick_reply.auto_function_calling", False)
        )

        # 快捷回复按钮显示配置
        self.button_min_width_px = 100  # 最小宽度（像素）
        self.button_max_width_px = 300  # 最大宽度（像素）
        self.button_max_text_length = 25  # 最大显示文本长度（字符）

        # 自动测试相关变量
        self.auto_select_enabled = tk.BooleanVar()  # 自动选择开关
        self.auto_execute_enabled = tk.BooleanVar()  # 自动执行开关
        self.auto_execute_rounds = tk.IntVar(value=10)  # 自动执行回合数
        self.current_auto_round = 0  # 当前自动执行回合计数
        self.auto_execute_timer_id = None  # 自动执行定时器ID
        self.auto_select_delay = tk.IntVar(value=3)  # 自动选择延迟（秒）
        self.auto_test_start_time = None  # 自动测试开始时间
        self.auto_test_log_file = None  # 自动测试日志文件路径
        
        self._setup_gui()
        self._setup_bindings()

        # 加载默认系统提示词
        self._load_default_prompt()

        # 显示欢迎信息，不自动创建会话
        self._show_welcome_message()
    
    def _setup_gui(self):
        """设置GUI界面"""
        print("[DEBUG] _setup_gui 开始")

        # 窗口配置
        self.root.title(config.get_app_setting("window.title", "iNiverse 平行世界客户端"))
        self.root.geometry(f"{self.window_width}x{self.window_height}")
        self.root.minsize(600, 400)
        print("[DEBUG] 窗口配置完成")

        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        print(f"[DEBUG] 主框架已创建: {self.main_frame}")

        # 创建顶部工具栏
        self._create_toolbar()
        print("[DEBUG] 工具栏创建完成")

        # 创建消息显示区域
        self._create_message_area()
        print("[DEBUG] 消息显示区域创建完成")

        # 创建快捷回复区域（在输入区域之前）
        self._create_quick_reply_area()
        print("[DEBUG] 快捷回复区域创建完成")

        # 创建输入区域
        self._create_input_area()
        print("[DEBUG] 输入区域创建完成")

        # 创建状态栏
        self._create_status_bar()
        print("[DEBUG] 状态栏创建完成")

        # 初始化温度显示
        self._on_temperature_changed()

        # 初始化Token限制显示
        self._update_token_limit_display(self.current_model.get())

        print("[DEBUG] _setup_gui 完成")
    
    def _create_toolbar(self):
        """创建顶部工具栏"""
        toolbar_frame = ttk.Frame(self.main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 模型选择
        ttk.Label(toolbar_frame, text="模型:").pack(side=tk.LEFT, padx=(0, 5))
        
        # 获取模型列表（包含价格信息）
        available_models = self.api_client.get_available_models()
        model_display_list = list(available_models.values())  # 显示完整名称和价格
        model_id_list = list(available_models.keys())  # 模型ID列表

        # 创建模型ID到显示名称的映射
        self.model_display_map = dict(zip(model_id_list, model_display_list))
        self.model_id_map = dict(zip(model_display_list, model_id_list))

        model_combo = ttk.Combobox(
            toolbar_frame,
            values=model_display_list,  # 显示包含价格的完整名称
            state="readonly",
            width=45  # 增加宽度以显示完整的价格信息
        )
        model_combo.pack(side=tk.LEFT, padx=(0, 10))
        model_combo.bind('<<ComboboxSelected>>', self._on_model_changed)

        # 设置初始模型选择
        default_model_id = config.get_app_setting("api.default_model", "deepseek-reasoner")
        if default_model_id in self.model_display_map:
            model_combo.set(self.model_display_map[default_model_id])
        elif model_display_list:
            model_combo.set(model_display_list[0])  # 如果默认模型不存在，选择第一个

        # 保存下拉框引用以便后续使用
        self.model_combo = model_combo
        
        # Max Tokens设置（智能步进调整）
        ttk.Label(toolbar_frame, text="最大Token:").pack(side=tk.LEFT, padx=(0, 5))

        # Token控件框架
        token_frame = ttk.Frame(toolbar_frame)
        token_frame.pack(side=tk.LEFT, padx=(0, 10))

        # Token步进序列
        self.token_steps = [512, 1024, 2048, 4096, 8192, 16384, 32768, 65536]

        # Token减少按钮
        self.token_down_btn = ttk.Button(
            token_frame,
            text="▼",
            width=3,
            command=self._decrease_tokens
        )
        self.token_down_btn.pack(side=tk.LEFT)

        # Token输入框
        self.token_entry = ttk.Entry(
            token_frame,
            textvariable=self.max_tokens,
            width=8,
            justify='center'
        )
        self.token_entry.pack(side=tk.LEFT, padx=(2, 2))
        self.token_entry.bind('<FocusOut>', self._validate_token_input)
        self.token_entry.bind('<Return>', self._validate_token_input)

        # Token增加按钮
        self.token_up_btn = ttk.Button(
            token_frame,
            text="▲",
            width=3,
            command=self._increase_tokens
        )
        self.token_up_btn.pack(side=tk.LEFT)

        # Token限制提示标签
        self.token_limit_label = ttk.Label(
            token_frame,
            text="",
            font=(self.font_family, self.font_size - 1),
            foreground="#666666"
        )
        self.token_limit_label.pack(side=tk.LEFT, padx=(5, 0))

        # 温度设置
        ttk.Label(toolbar_frame, text="温度:").pack(side=tk.LEFT, padx=(0, 5))

        # 创建温度滑动条框架
        temp_frame = ttk.Frame(toolbar_frame)
        temp_frame.pack(side=tk.LEFT, padx=(0, 5))

        # 温度滑动条
        self.temp_scale = tk.Scale(
            temp_frame,
            from_=0.1,
            to=2.0,
            resolution=0.1,
            orient=tk.HORIZONTAL,
            variable=self.temperature,
            length=100,
            showvalue=0  # 不显示内置数值
        )
        self.temp_scale.pack(side=tk.LEFT)

        # 温度数值显示标签
        self.temp_value_label = ttk.Label(temp_frame, text="0.7", width=4)
        self.temp_value_label.pack(side=tk.LEFT, padx=(5, 10))

        # 绑定温度变化事件
        self.temperature.trace('w', self._on_temperature_changed)
        
        # 系统提示词文件选择区域
        prompt_frame = ttk.LabelFrame(toolbar_frame, text="系统提示词文件", padding=5)
        prompt_frame.pack(side=tk.LEFT, padx=(0, 10), fill=tk.Y)

        # 显示当前选择的文件名 - 使用更显眼的样式
        self.prompt_file_label = ttk.Label(
            prompt_frame,
            textvariable=self.current_system_prompt_file,
            width=25,
            relief=tk.SUNKEN,
            anchor=tk.W,
            background="#f0f0f0",
            foreground="#000080",
            font=(self.font_family, self.font_size, "bold")
        )
        self.prompt_file_label.pack(side=tk.TOP, pady=(0, 3))

        # 选择文件按钮
        select_prompt_btn = ttk.Button(
            prompt_frame,
            text="选择文件",
            command=self._select_prompt_file
        )
        select_prompt_btn.pack(side=tk.TOP)
        
        # 新建会话按钮
        new_session_btn = ttk.Button(
            toolbar_frame,
            text="新建会话",
            command=self._create_new_session
        )
        new_session_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 开始游戏按钮
        self.start_game_btn = ttk.Button(
            toolbar_frame,
            text="开始游戏",
            command=self._start_game,
            state=tk.DISABLED  # 初始状态为禁用
        )
        self.start_game_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 保存历史按钮
        save_history_btn = ttk.Button(
            toolbar_frame,
            text="保存历史",
            command=self._save_history_to_file
        )
        save_history_btn.pack(side=tk.LEFT, padx=(0, 5))

        # Function Calling开关按钮
        self.function_calling_var = tk.BooleanVar(value=self.api_client.is_function_calling_enabled())
        self.function_calling_btn = ttk.Checkbutton(
            toolbar_frame,
            text="Function Calling",
            variable=self.function_calling_var,
            command=self._toggle_function_calling
        )
        self.function_calling_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 保存进度按钮
        save_progress_btn = ttk.Button(
            toolbar_frame,
            text="保存进度",
            command=self._save_progress
        )
        save_progress_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 读取进度按钮
        load_progress_btn = ttk.Button(
            toolbar_frame,
            text="读取进度",
            command=self._load_progress
        )
        load_progress_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 清空历史按钮
        clear_btn = ttk.Button(
            toolbar_frame,
            text="清空历史",
            command=self._clear_history
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 自动测试控制面板
        self._create_auto_test_panel(toolbar_frame)

    def _create_auto_test_panel(self, parent_frame):
        """创建自动测试控制面板"""
        # 自动测试控制框架
        auto_test_frame = ttk.LabelFrame(parent_frame, text="自动测试", padding=5)
        auto_test_frame.pack(side=tk.LEFT, padx=(10, 0), fill=tk.Y)

        # 第一行：自动选择控制
        row1_frame = ttk.Frame(auto_test_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 3))

        # 自动选择开关
        self.auto_select_checkbox = ttk.Checkbutton(
            row1_frame,
            text="自动选择",
            variable=self.auto_select_enabled,
            command=self._on_auto_select_toggle
        )
        self.auto_select_checkbox.pack(side=tk.LEFT)

        # 延迟设置
        ttk.Label(row1_frame, text="延迟:").pack(side=tk.LEFT, padx=(10, 2))
        delay_spinbox = ttk.Spinbox(
            row1_frame,
            from_=1,
            to=10,
            width=3,
            textvariable=self.auto_select_delay
        )
        delay_spinbox.pack(side=tk.LEFT)
        ttk.Label(row1_frame, text="秒").pack(side=tk.LEFT, padx=(2, 0))

        # 第二行：自动执行控制
        row2_frame = ttk.Frame(auto_test_frame)
        row2_frame.pack(fill=tk.X, pady=(0, 3))

        # 自动执行开关
        self.auto_execute_checkbox = ttk.Checkbutton(
            row2_frame,
            text="自动执行",
            variable=self.auto_execute_enabled,
            command=self._on_auto_execute_toggle
        )
        self.auto_execute_checkbox.pack(side=tk.LEFT)

        # 回合数设置
        ttk.Label(row2_frame, text="回合:").pack(side=tk.LEFT, padx=(10, 2))
        rounds_spinbox = ttk.Spinbox(
            row2_frame,
            from_=1,
            to=1000,
            width=4,
            textvariable=self.auto_execute_rounds
        )
        rounds_spinbox.pack(side=tk.LEFT)

        # 第三行：控制按钮和状态
        row3_frame = ttk.Frame(auto_test_frame)
        row3_frame.pack(fill=tk.X)

        # 开始/停止按钮
        self.auto_test_control_btn = ttk.Button(
            row3_frame,
            text="开始自动测试",
            command=self._toggle_auto_test,
            state=tk.DISABLED
        )
        self.auto_test_control_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 状态显示
        self.auto_test_status_label = ttk.Label(
            row3_frame,
            text="未启用",
            font=(self.font_family, self.font_size - 1),
            foreground="#666666"
        )
        self.auto_test_status_label.pack(side=tk.LEFT)

    def _create_message_area(self):
        """创建消息显示区域"""
        # 计算消息区域高度（占总高度的75%）
        message_height = int(self.window_height * 0.75)
        
        # 创建滚动文本框
        self.message_text = scrolledtext.ScrolledText(
            self.main_frame,
            wrap=tk.WORD,
            font=(self.font_family, self.font_size),
            state=tk.DISABLED,
            height=message_height // 20  # 大概估算行数
        )
        self.message_text.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 配置文本标签样式
        self.message_text.tag_configure("user", foreground=self.user_color, font=(self.font_family, self.font_size, "bold"))
        self.message_text.tag_configure("assistant", foreground=self.assistant_color, font=(self.font_family, self.font_size))
        self.message_text.tag_configure("thinking", foreground=self.thinking_color, font=(self.font_family, self.font_size, "italic"))
        self.message_text.tag_configure("system", foreground="#888888", font=(self.font_family, self.font_size - 1))
        self.message_text.tag_configure("timestamp", foreground="#999999", font=(self.font_family, self.font_size - 2))
        self.message_text.tag_configure("function_call", foreground="#ff6600", font=(self.font_family, self.font_size, "bold"))
    
    def _create_quick_reply_area(self):
        """创建快捷回复区域"""
        print("[DEBUG] _create_quick_reply_area 开始")
        self.quick_reply_frame = ttk.Frame(self.main_frame)
        print(f"[DEBUG] 快捷回复框架已创建: {self.quick_reply_frame}")

        # 创建Function Calling复选框控制区域
        self.function_calling_control_frame = ttk.Frame(self.main_frame)
        self.function_calling_control_frame.pack(fill=tk.X, pady=(5, 0))

        # 添加Function Calling自动调用复选框
        self.auto_function_calling_checkbox = ttk.Checkbutton(
            self.function_calling_control_frame,
            text="自动调用Function Calling显示游戏数据",
            variable=self.auto_call_function_calling,
            command=self._on_auto_function_calling_changed
        )
        self.auto_function_calling_checkbox.pack(side=tk.LEFT, padx=(10, 0))

        # 添加工具提示标签
        tooltip_label = ttk.Label(
            self.function_calling_control_frame,
            text="💡 勾选后，点击快捷回复将自动追加Function Calling调用",
            font=(self.font_family, self.font_size - 1),
            foreground="#666666"
        )
        tooltip_label.pack(side=tk.LEFT, padx=(10, 0))

        # 不立即pack快捷回复框架，让_show_quick_reply_buttons方法来控制显示
        print("[DEBUG] _create_quick_reply_area 完成")
    
    def _create_input_area(self):
        """创建输入区域"""
        self.input_frame = ttk.Frame(self.main_frame)
        self.input_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 输入文本框
        self.input_text = tk.Text(
            self.input_frame,
            height=3,
            font=(self.font_family, self.font_size),
            wrap=tk.WORD
        )
        self.input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 发送按钮
        self.send_button = ttk.Button(
            self.input_frame,
            text="发送",
            command=self._send_message
        )
        self.send_button.pack(side=tk.RIGHT)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(
            self.main_frame,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
    
    def _setup_bindings(self):
        """设置事件绑定"""
        # Enter键发送消息
        self.input_text.bind('<Return>', self._on_enter_key)

        # Ctrl+Enter发送消息（备选）
        self.root.bind('<Control-Return>', lambda e: self._send_message())

        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

    def _on_enter_key(self, event):
        """处理Enter键按下事件"""
        # 阻止默认的换行行为
        self._send_message()
        return "break"

    def _load_default_prompt(self):
        """加载默认系统提示词"""
        try:
            # 首先尝试从配置中获取上次使用的文件名
            last_prompt_file = config.get_last_prompt_file()

            # 如果有上次使用的文件名且不是默认提示词，尝试加载该文件
            if last_prompt_file != "(默认提示词)":
                # 尝试在项目根目录查找该文件
                prompt_file_path = config.project_root / last_prompt_file
                if prompt_file_path.exists():
                    self.current_system_prompt_content = config.load_prompt_from_file(str(prompt_file_path))
                    self.current_system_prompt_file.set(last_prompt_file)
                    # 解析并缓存剧情必要输出内容
                    self.cached_plot_output_content = self._parse_plot_output_content(self.current_system_prompt_content)
                    self.status_var.set(f"已加载上次使用的提示词文件: {last_prompt_file}")
                    return

            # 尝试加载system_prompt.txt文件
            default_prompt_file = config.project_root / "system_prompt.txt"
            if default_prompt_file.exists():
                self.current_system_prompt_content = config.load_prompt_from_file(str(default_prompt_file))
                self.current_system_prompt_file.set("system_prompt.txt")
                config.set_last_prompt_file("system_prompt.txt")
                # 解析并缓存剧情必要输出内容
                self.cached_plot_output_content = self._parse_plot_output_content(self.current_system_prompt_content)
                self.status_var.set("已加载默认提示词文件")
            else:
                # 使用默认提示词
                self.current_system_prompt_content = "你是一个有用的AI助手，请用中文回答问题。"
                self.current_system_prompt_file.set("(默认提示词)")
                config.set_last_prompt_file("(默认提示词)")
                self.cached_plot_output_content = ""  # 默认提示词没有剧情输出内容
                self.status_var.set("使用默认提示词")

        except Exception as e:
            print(f"加载默认提示词失败: {e}")
            self.current_system_prompt_content = "你是一个有用的AI助手，请用中文回答问题。"
            self.current_system_prompt_file.set("(默认提示词)")
            config.set_last_prompt_file("(默认提示词)")
            self.cached_plot_output_content = ""  # 异常情况下清空缓存
            self.status_var.set("使用默认提示词")

    def _show_welcome_message(self):
        """显示欢迎信息"""
        try:
            # 清空消息显示区域
            self._clear_message_display()

            # 显示欢迎信息
            self.message_text.config(state=tk.NORMAL)

            welcome_text = """
欢迎使用 iNiverse 平行世界客户端！

使用说明：
1. 点击"新建会话"按钮开始新的对话
2. 可以通过"选择文件"按钮加载自定义系统提示词
3. 在工具栏中可以调整模型参数（最大Token、温度等）
4. 支持保存和读取对话进度

当前系统提示词文件：{prompt_file}

请点击"新建会话"按钮开始对话。
""".format(prompt_file=self.current_system_prompt_file.get())

            self.message_text.insert(tk.END, welcome_text, "system")
            self.message_text.config(state=tk.DISABLED)

            # 禁用输入区域，直到创建会话
            self._disable_input_area()

            # 禁用开始游戏按钮
            self.start_game_btn.config(state=tk.DISABLED)

            # 隐藏快捷回复按钮
            self._hide_quick_reply_buttons()

            self.status_var.set("请点击\"新建会话\"开始对话")

        except Exception as e:
            print(f"显示欢迎信息失败: {e}")

    def _disable_input_area(self):
        """禁用输入区域"""
        try:
            self.input_text.config(state=tk.DISABLED)
            self.send_button.config(state=tk.DISABLED)
        except Exception as e:
            print(f"禁用输入区域失败: {e}")

    def _enable_input_area(self):
        """启用输入区域"""
        try:
            self.input_text.config(state=tk.NORMAL)
            self.send_button.config(state=tk.NORMAL)
            self.input_text.focus_set()
        except Exception as e:
            print(f"启用输入区域失败: {e}")

    def _create_new_session(self):
        """创建新会话"""
        print("[DEBUG] _create_new_session 开始")
        try:
            # 使用当前加载的系统提示词内容
            system_prompt = self.current_system_prompt_content
            print(f"[DEBUG] 系统提示词内容长度: {len(system_prompt) if system_prompt else 0}")

            # 创建新会话
            session = self.history_manager.create_new_session(
                model=self.current_model.get(),
                system_prompt=system_prompt,
                title=f"对话 {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            )

            # 清空消息显示区域
            self._clear_message_display()

            # 启用输入区域
            self._enable_input_area()

            # 更新状态
            self.status_var.set(f"新会话已创建: {session.title}")

            # 隐藏快捷回复按钮
            self._hide_quick_reply_buttons()

            # 显示会话开始提示，但不自动发送消息
            self.message_text.config(state=tk.NORMAL)
            self.message_text.insert(tk.END, "会话已创建，您可以开始对话了。\n\n", "system")
            self.message_text.config(state=tk.DISABLED)
            print("[DEBUG] 会话创建提示已显示")

            # 显示默认快捷回复按钮
            print("[DEBUG] 准备调用 _show_default_quick_replies")
            self._show_default_quick_replies()
            print("[DEBUG] _show_default_quick_replies 调用完成")

            # 启用开始游戏按钮
            self.start_game_btn.config(state=tk.NORMAL)
            print("[DEBUG] 开始游戏按钮已启用")

        except Exception as e:
            messagebox.showerror("错误", f"创建新会话失败: {str(e)}")

    def _start_game(self):
        """开始游戏 - 自动发送"开始新游戏"消息"""
        print("[DEBUG] _start_game 被调用")
        try:
            # 检查是否有活动会话
            if not self.history_manager.current_session:
                messagebox.showwarning("提示", "请先创建新会话")
                return

            # 设置输入框内容
            self.input_text.delete(1.0, tk.END)
            self.input_text.insert(1.0, "开始新游戏")

            # 自动发送消息
            self._send_message()

        except Exception as e:
            print(f"开始游戏失败: {e}")
            messagebox.showerror("错误", f"开始游戏失败: {str(e)}")

    def _show_default_quick_replies(self):
        """显示默认快捷回复按钮"""
        try:
            # 从配置中获取默认快捷回复选项
            default_options = config.get_app_setting("ui.quick_reply.default_options", [])
            print(f"[DEBUG] 获取到的默认快捷回复选项: {default_options}")
            if default_options:
                print(f"[DEBUG] 调用 _show_quick_reply_buttons 显示默认选项")
                self._show_quick_reply_buttons(default_options)
            else:
                print(f"[DEBUG] 没有默认快捷回复选项")
        except Exception as e:
            print(f"显示默认快捷回复失败: {e}")
            import traceback
            traceback.print_exc()

    def _select_prompt_file(self):
        """选择系统提示词文件"""
        try:
            # 打开文件选择对话框
            file_path = filedialog.askopenfilename(
                title="选择系统提示词文件",
                filetypes=[
                    ("文本文件", "*.txt"),
                    ("所有文件", "*.*")
                ],
                initialdir=str(config.project_root)
            )

            if file_path:
                # 加载并处理提示词文件
                try:
                    self.current_system_prompt_content = config.load_prompt_from_file(file_path)

                    # 解析并缓存剧情必要输出内容
                    self.cached_plot_output_content = self._parse_plot_output_content(self.current_system_prompt_content)

                    # 更新界面显示
                    file_name = Path(file_path).name
                    self.current_system_prompt_file.set(file_name)

                    # 持久化保存文件名
                    config.set_last_prompt_file(file_name)

                    # 更新状态
                    self.status_var.set(f"已加载提示词文件: {file_name}")

                    messagebox.showinfo("成功", f"已成功加载提示词文件: {file_name}")

                except Exception as e:
                    messagebox.showerror("错误", f"加载提示词文件失败: {str(e)}")

        except Exception as e:
            messagebox.showerror("错误", f"选择文件失败: {str(e)}")

    def _save_history_to_file(self):
        """保存聊天历史到TXT文件"""
        try:
            # 获取消息显示区域的所有文本内容
            message_content = self.message_text.get(1.0, tk.END)

            # 如果内容为空或只有换行符，提示用户
            if not message_content.strip():
                messagebox.showwarning("提示", "当前没有聊天记录可以保存")
                return

            # 创建UserBook文件夹
            userbook_dir = config.project_root / "UserBook"
            userbook_dir.mkdir(exist_ok=True)

            # 生成文件名（时间戳格式）
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}.txt"
            file_path = userbook_dir / filename

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                # 添加文件头部信息
                f.write("=" * 50 + "\n")
                f.write("iNiverse 平行世界客户端 - 对话历史记录\n")
                f.write(f"保存时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")

                # 写入聊天内容
                f.write(message_content)

            # 显示成功提示
            messagebox.showinfo(
                "保存成功",
                f"聊天历史已保存到:\n{file_path}\n\n文件大小: {len(message_content.encode('utf-8'))} 字节"
            )

            # 更新状态栏
            self.status_var.set(f"历史记录已保存: {filename}")

        except Exception as e:
            # 显示错误提示
            messagebox.showerror("保存失败", f"保存聊天历史时发生错误:\n{str(e)}")
            print(f"保存历史记录失败: {e}")

    def _save_progress(self):
        """保存当前会话进度到JSON文件"""
        try:
            # 创建progress文件夹
            progress_dir = config.project_root / "progress"
            progress_dir.mkdir(exist_ok=True)

            # 生成文件名（时间戳格式）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"progress_{timestamp}.json"
            file_path = progress_dir / filename

            # 收集当前快捷回复按钮状态
            current_quick_replies = []
            if hasattr(self, 'quick_reply_buttons') and self.quick_reply_buttons:
                # 跳过第一个元素（标签），只收集按钮文本
                for widget in self.quick_reply_buttons[1:]:  # 跳过"快捷回复:"标签
                    if hasattr(widget, 'cget'):
                        try:
                            button_text = widget.cget('text')
                            if button_text:
                                current_quick_replies.append(button_text)
                        except:
                            pass

            # 构建进度数据
            progress_data = {
                "version": "1.0",
                "saved_at": datetime.now().isoformat(),
                "session_info": {
                    "session_id": self.history_manager.current_session.session_id if self.history_manager.current_session else None,
                    "title": self.history_manager.current_session.title if self.history_manager.current_session else "未命名会话",
                    "created_at": self.history_manager.current_session.created_at if self.history_manager.current_session else time.time(),
                    "updated_at": time.time()
                },
                "model_settings": {
                    "model": self.current_model.get(),
                    "temperature": self.temperature.get(),
                    "max_tokens": self.max_tokens.get()
                },
                "system_prompt": {
                    "file": self.current_system_prompt_file.get(),
                    "content": self.current_system_prompt_content
                },
                "messages": [],
                "quick_replies": current_quick_replies,
                "ui_state": {
                    "quick_reply_visible": len(current_quick_replies) > 0
                }
            }

            # 添加消息历史
            if self.history_manager.current_session:
                for msg in self.history_manager.current_session.messages:
                    progress_data["messages"].append({
                        "role": msg.role,
                        "content": msg.content,
                        "timestamp": msg.timestamp
                    })

            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)

            # 显示成功提示
            messagebox.showinfo(
                "保存成功",
                f"进度已保存到:\n{file_path}\n\n包含内容:\n"
                f"- 消息历史: {len(progress_data['messages'])} 条\n"
                f"- 快捷回复: {len(current_quick_replies)} 个\n"
                f"- 模型设置: {progress_data['model_settings']['model']}"
            )

            # 更新状态栏
            self.status_var.set(f"进度已保存: {filename}")

        except Exception as e:
            # 显示错误提示
            messagebox.showerror("保存失败", f"保存进度时发生错误:\n{str(e)}")
            print(f"保存进度失败: {e}")

    def _load_progress(self):
        """从JSON文件读取并恢复会话进度"""
        try:
            # 打开文件选择对话框
            progress_dir = config.project_root / "progress"
            if not progress_dir.exists():
                progress_dir.mkdir(exist_ok=True)

            file_path = filedialog.askopenfilename(
                title="选择进度文件",
                initialdir=str(progress_dir),
                filetypes=[
                    ("JSON文件", "*.json"),
                    ("所有文件", "*.*")
                ]
            )

            if not file_path:
                return  # 用户取消了选择

            # 显示加载状态
            self.status_var.set("正在加载进度...")
            self.root.update()

            # 读取并解析文件
            with open(file_path, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)

            # 验证文件格式
            if not self._validate_progress_data(progress_data):
                messagebox.showerror("格式错误", "选择的文件不是有效的进度存档文件")
                return

            # 恢复会话数据
            self._restore_progress_data(progress_data)

            # 显示成功提示
            filename = Path(file_path).name
            messagebox.showinfo(
                "加载成功",
                f"进度已从以下文件加载:\n{filename}\n\n恢复内容:\n"
                f"- 消息历史: {len(progress_data.get('messages', []))} 条\n"
                f"- 快捷回复: {len(progress_data.get('quick_replies', []))} 个\n"
                f"- 模型设置: {progress_data.get('model_settings', {}).get('model', '未知')}"
            )

            # 更新状态栏
            self.status_var.set(f"进度已加载: {filename}")

        except json.JSONDecodeError as e:
            messagebox.showerror("文件错误", f"文件格式错误，无法解析JSON:\n{str(e)}")
        except Exception as e:
            messagebox.showerror("加载失败", f"加载进度时发生错误:\n{str(e)}")
            print(f"加载进度失败: {e}")

    def _validate_progress_data(self, data: Dict[str, Any]) -> bool:
        """验证进度数据的有效性"""
        try:
            # 检查必需的字段
            required_fields = ["version", "saved_at", "session_info", "model_settings", "messages"]
            for field in required_fields:
                if field not in data:
                    print(f"缺少必需字段: {field}")
                    return False

            # 检查版本兼容性
            version = data.get("version", "")
            if not version.startswith("1."):
                print(f"不支持的版本: {version}")
                return False

            # 检查消息格式
            messages = data.get("messages", [])
            if not isinstance(messages, list):
                print("消息格式错误")
                return False

            for msg in messages:
                if not isinstance(msg, dict) or "role" not in msg or "content" not in msg:
                    print("消息项格式错误")
                    return False

            return True

        except Exception as e:
            print(f"验证进度数据时出错: {e}")
            return False

    def _restore_progress_data(self, data: Dict[str, Any]):
        """恢复进度数据到当前会话"""
        try:
            # 1. 恢复模型设置
            model_settings = data.get("model_settings", {})
            if "model" in model_settings:
                self.current_model.set(model_settings["model"])
            if "temperature" in model_settings:
                self.temperature.set(model_settings["temperature"])
                self._on_temperature_changed()  # 更新显示
            if "max_tokens" in model_settings:
                self.max_tokens.set(model_settings["max_tokens"])

            # 2. 恢复系统提示词
            system_prompt = data.get("system_prompt", {})
            if "file" in system_prompt:
                self.current_system_prompt_file.set(system_prompt["file"])
            if "content" in system_prompt:
                self.current_system_prompt_content = system_prompt["content"]

            # 3. 创建新会话或更新当前会话
            session_info = data.get("session_info", {})

            # 清空当前显示
            self._clear_message_display()
            self._hide_quick_reply_buttons()

            # 创建新的会话对象
            from .chat_history import ChatSession
            import time

            messages = []
            for msg_data in data.get("messages", []):
                msg = ChatMessage(
                    role=msg_data["role"],
                    content=msg_data["content"],
                    timestamp=msg_data.get("timestamp")
                )
                messages.append(msg)

            # 创建会话
            session = ChatSession(
                session_id=session_info.get("session_id", f"restored_{int(time.time())}"),
                title=session_info.get("title", "已恢复的会话"),
                created_at=session_info.get("created_at", time.time()),
                updated_at=time.time(),
                model=model_settings.get("model", self.current_model.get()),
                system_prompt=system_prompt.get("content", ""),
                messages=messages
            )

            # 设置为当前会话
            self.history_manager.current_session = session

            # 4. 恢复消息显示
            for msg in messages:
                self._display_message(msg.role, msg.content)

            # 5. 恢复快捷回复按钮
            quick_replies = data.get("quick_replies", [])
            if quick_replies:
                self._show_quick_reply_buttons(quick_replies)

            print(f"成功恢复进度: {len(messages)} 条消息, {len(quick_replies)} 个快捷回复")

        except Exception as e:
            print(f"恢复进度数据时出错: {e}")
            raise

    def _on_model_changed(self, event=None):
        """模型选择改变时的回调函数"""
        try:
            # 获取选中的显示名称
            selected_display_name = self.model_combo.get()

            # 转换为模型ID
            selected_model_id = self.model_id_map.get(selected_display_name)
            if not selected_model_id:
                print(f"无法找到模型ID: {selected_display_name}")
                return

            # 更新内部状态
            self.current_model.set(selected_model_id)

            # 使用统一客户端获取推荐的Token数量
            recommended_tokens = self.api_client.get_recommended_max_tokens(selected_model_id)

            # 确保Token值在安全范围内
            safe_tokens = self.api_client.get_safe_token_value(selected_model_id, recommended_tokens)
            self.max_tokens.set(safe_tokens)

            # 更新Token限制提示
            self._update_token_limit_display(selected_model_id)

            # 获取模型信息
            model_info = self.api_client.get_model_info(selected_model_id)
            provider = model_info.get("provider", "unknown")
            price = model_info.get("price", 0)

            print(f"模型切换到: {selected_model_id} (显示: {selected_display_name})")
            print(f"  提供商: {provider}, 价格: {price}, 最大Token: {self.max_tokens.get()}")

        except Exception as e:
            print(f"模型切换处理失败: {e}")

    def get_current_model_id(self) -> str:
        """获取当前选择的模型ID"""
        return self.current_model.get()

    def _update_token_limit_display(self, model: str):
        """更新Token限制显示"""
        try:
            max_limit = self.api_client.get_model_max_tokens(model)
            self.token_limit_label.config(text=f"(最大: {max_limit:,})")
        except Exception as e:
            print(f"更新Token限制显示失败: {e}")
            self.token_limit_label.config(text="")

    def _get_current_token_step_index(self) -> int:
        """获取当前Token值在步进序列中的索引"""
        current_tokens = self.max_tokens.get()

        # 找到最接近的步进值
        for i, step in enumerate(self.token_steps):
            if current_tokens <= step:
                return i

        # 如果超过最大步进值，返回最后一个索引
        return len(self.token_steps) - 1

    def _increase_tokens(self):
        """增加Token数量（按步进序列）"""
        try:
            current_model = self.current_model.get()
            max_limit = self.api_client.get_model_max_tokens(current_model)
            current_index = self._get_current_token_step_index()

            # 找到下一个可用的步进值
            for i in range(current_index + 1, len(self.token_steps)):
                next_tokens = self.token_steps[i]
                if next_tokens <= max_limit:
                    self.max_tokens.set(next_tokens)
                    print(f"Token增加到: {next_tokens}")
                    return

            # 如果没有更大的可用步进值，设置为最大限制
            if self.max_tokens.get() < max_limit:
                self.max_tokens.set(max_limit)
                print(f"Token设置为最大限制: {max_limit}")

        except Exception as e:
            print(f"增加Token时出错: {e}")

    def _decrease_tokens(self):
        """减少Token数量（按步进序列）"""
        try:
            current_index = self._get_current_token_step_index()
            current_tokens = self.max_tokens.get()

            # 如果当前值正好等于某个步进值，则选择前一个
            if current_index > 0 and current_tokens == self.token_steps[current_index]:
                new_tokens = self.token_steps[current_index - 1]
            elif current_index > 0:
                # 如果当前值在两个步进值之间，选择较小的那个
                new_tokens = self.token_steps[current_index - 1]
            else:
                # 已经是最小值
                new_tokens = self.token_steps[0]

            self.max_tokens.set(new_tokens)
            print(f"Token减少到: {new_tokens}")

        except Exception as e:
            print(f"减少Token时出错: {e}")

    def _validate_token_input(self, event=None):
        """验证Token输入值"""
        try:
            current_model = self.current_model.get()
            requested_tokens = self.max_tokens.get()

            # 获取安全的Token值
            safe_tokens = self.api_client.get_safe_token_value(current_model, requested_tokens)

            if safe_tokens != requested_tokens:
                self.max_tokens.set(safe_tokens)
                max_limit = self.api_client.get_model_max_tokens(current_model)

                if requested_tokens > max_limit:
                    print(f"Token值超过限制，已调整为最大值: {safe_tokens}")
                elif requested_tokens < 512:
                    print(f"Token值低于最小值，已调整为: {safe_tokens}")

        except Exception as e:
            print(f"验证Token输入时出错: {e}")
            # 发生错误时设置为默认值
            self.max_tokens.set(8192)

    def _on_temperature_changed(self, *args):
        """温度值改变时的回调函数"""
        try:
            temp_value = self.temperature.get()
            # 更新显示标签
            self.temp_value_label.config(text=f"{temp_value:.1f}")

        except Exception as e:
            print(f"温度值更新失败: {e}")

    def _on_auto_function_calling_changed(self):
        """自动Function Calling复选框状态变化时的回调函数"""
        try:
            # 保存设置到配置文件
            config.set_app_setting("ui.quick_reply.auto_function_calling", self.auto_call_function_calling.get())
            config.save_app_settings()

            # 更新状态栏提示
            if self.auto_call_function_calling.get():
                self.status_var.set("已启用：快捷回复自动调用Function Calling")
            else:
                self.status_var.set("已禁用：快捷回复自动调用Function Calling")

            print(f"[DEBUG] Function Calling自动调用设置已更新: {self.auto_call_function_calling.get()}")

        except Exception as e:
            print(f"保存Function Calling设置失败: {e}")

    def _calculate_text_width(self, text: str) -> int:
        """计算文本显示宽度（中文字符按2个字符计算）"""
        width = 0
        for char in text:
            if '\u4e00' <= char <= '\u9fff':  # 中文字符
                width += 2
            else:  # 英文字符和其他字符
                width += 1
        return width

    def _truncate_text(self, text: str, max_length: int) -> str:
        """智能截断文本并添加省略号"""
        if len(text) <= max_length:
            return text

        # 计算实际显示宽度
        current_width = 0
        truncate_pos = 0

        for i, char in enumerate(text):
            char_width = 2 if '\u4e00' <= char <= '\u9fff' else 1
            if current_width + char_width > max_length - 3:  # 预留省略号空间
                truncate_pos = i
                break
            current_width += char_width
            truncate_pos = i + 1

        return text[:truncate_pos] + "..."

    def _calculate_button_width_chars(self, text: str) -> int:
        """根据文本内容计算按钮的字符宽度（用于ttk.Button的width参数）"""
        # 计算文本的显示宽度（中文字符按2个字符计算）
        text_width = self._calculate_text_width(text)

        # 添加一些内边距（左右各2个字符的空间）
        total_width = text_width + 4

        # 设置合理的最小和最大字符宽度
        min_chars = 8   # 最小8个字符宽度
        max_chars = 40  # 最大40个字符宽度

        return max(min_chars, min(max_chars, total_width))

    def _auto_send_new_game_message(self):
        """自动发送新游戏开始消息"""
        try:
            # 设置输入框内容
            self.input_text.delete(1.0, tk.END)
            self.input_text.insert(1.0, "新游戏开始")

            # 自动发送消息
            self._send_message()

        except Exception as e:
            print(f"自动发送新游戏消息失败: {e}")

    def _clear_history(self):
        """清空当前会话历史"""
        if messagebox.askyesno("确认", "确定要清空当前会话的历史记录吗？"):
            self._clear_message_display()
            self._hide_quick_reply_buttons()
            self.status_var.set("历史记录已清空")
    
    def _clear_message_display(self):
        """清空消息显示区域"""
        self.message_text.config(state=tk.NORMAL)
        self.message_text.delete(1.0, tk.END)
        self.message_text.config(state=tk.DISABLED)

    def _get_thinking_mode_for_model(self, model: str) -> str:
        """根据模型确定thinking模式"""
        # 只有豆包种子1.6思考版真正支持thinking参数
        thinking_supported_models = [
            "doubao-seed-1-6-thinking-250715"  # 只有思考版支持thinking参数
        ]

        if model in thinking_supported_models:
            # 对于支持thinking的豆包模型，使用enabled模式确保开启思考
            return "enabled"
        else:
            # 其他模型不需要thinking参数
            return "disabled"

    def _send_message(self, is_quick_reply: bool = False):
        """发送消息"""
        print(f"[DEBUG] _send_message 被调用, is_quick_reply={is_quick_reply}")
        if self.is_sending:
            print("[DEBUG] 正在发送中，返回")
            return

        # 获取输入内容
        message_content = self.input_text.get(1.0, tk.END).strip()
        if not message_content:
            return

        # 如果是快捷回复且有缓存的剧情输出内容，则附加到消息中
        final_message_content = message_content
        if is_quick_reply and self.cached_plot_output_content:
            final_message_content = f"{message_content}\n\n{self.cached_plot_output_content}"
            print(f"[DEBUG] 快捷回复附加剧情输出内容: {self.cached_plot_output_content}")

        # 清空输入框
        self.input_text.delete(1.0, tk.END)

        # 显示用户消息（显示原始内容，不包含剧情输出内容）
        self._display_message("user", message_content)

        # 添加到历史记录（包含剧情输出内容）
        self.history_manager.add_message("user", final_message_content)
        
        # 在新线程中发送API请求
        self.is_sending = True
        self.send_button.config(state=tk.DISABLED)
        self.status_var.set("正在发送...")
        
        # 隐藏之前的快捷回复按钮
        self._hide_quick_reply_buttons()
        
        thread = threading.Thread(target=self._send_api_request)
        thread.daemon = True
        thread.start()
    
    def _send_api_request(self):
        """在后台线程中发送API请求"""
        try:
            # 获取上下文消息
            messages = self.history_manager.get_context_messages()
            
            # 准备显示助手回复
            self.root.after(0, lambda: self._start_assistant_message())
            
            assistant_content = ""
            thinking_content = ""
            cache_usage = None

            # 发送流式请求 - 添加详细调试信息
            selected_model = self.current_model.get()
            provider = self.api_client.get_provider_for_model(selected_model)
            client = self.api_client.get_client_for_model(selected_model)

            # 获取thinking模式
            thinking_mode = self._get_thinking_mode_for_model(selected_model)

            print(f"[DEBUG] API调用详情:")
            print(f"  选择的模型: {selected_model}")
            print(f"  识别的提供商: {provider}")
            print(f"  使用的客户端: {type(client).__name__}")
            if hasattr(client, 'base_url'):
                print(f"  API端点: {client.base_url}")
            print(f"  Token数量: {self.max_tokens.get()}")
            print(f"  温度: {self.temperature.get()}")
            print(f"  思考模式: {thinking_mode}")

            # 获取可用函数（如果Function Calling启用）
            functions = None
            if self.function_calling_var.get():
                functions = self.api_client.get_available_functions()
                print(f"[Function Calling] 可用函数数量: {len(functions) if functions else 0}")

            for chunk in self.api_client.chat_completion(
                messages=messages,
                model=selected_model,
                max_tokens=self.max_tokens.get(),
                temperature=self.temperature.get(),
                stream=True,
                thinking_mode=thinking_mode,
                functions=functions
            ):
                if chunk.error:
                    self.root.after(0, lambda e=chunk.error: self._handle_api_error(e))
                    return

                if chunk.is_complete:
                    # 请求完成，获取缓存使用情况
                    cache_usage = chunk.cache_usage
                    break

                # 处理函数调用显示
                if chunk.is_function_call and chunk.content:
                    self.root.after(0, lambda c=chunk.content: self._display_function_call_message(c))

                if chunk.content is not None:
                    if chunk.is_thinking:
                        thinking_content += chunk.content
                        self.root.after(0, lambda c=chunk.content: self._append_thinking_content(c))
                    elif not chunk.is_function_call:  # 不是函数调用显示的内容才添加到正文
                        assistant_content += chunk.content
                        self.root.after(0, lambda c=chunk.content: self._append_assistant_content(c))
            
            # 显示缓存使用情况
            if cache_usage:
                self.root.after(0, lambda cu=cache_usage: self._display_cache_usage(cu))

            # 添加到历史记录 - 只保存正文内容（content字段），不保存思考过程（reasoning_content字段）
            print(f"[DEBUG] 思考内容长度: {len(thinking_content)} 字符（仅显示，不保存）")
            print(f"[DEBUG] 正文内容长度: {len(assistant_content)} 字符（保存到历史记录）")

            # 根据DeepSeek官方文档，只将content字段保存到历史记录，reasoning_content仅用于显示
            self.history_manager.add_message("assistant", assistant_content)

            # 解析快捷回复选项
            self.root.after(0, lambda: self._parse_and_show_quick_replies(assistant_content))
            
        except Exception as e:
            self.root.after(0, lambda: self._handle_api_error(str(e)))
        finally:
            # 恢复发送按钮
            self.root.after(0, self._finish_sending)
    
    def _start_assistant_message(self):
        """开始显示游戏管理消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.message_text.config(state=tk.NORMAL)
        self.message_text.insert(tk.END, f"\n游戏管理 [{timestamp}]:\n", "assistant")
        self.message_text.config(state=tk.DISABLED)
        self.message_text.see(tk.END)
    
    def _append_thinking_content(self, content: str):
        """追加思考过程内容"""
        if content is not None:
            self.message_text.config(state=tk.NORMAL)
            self.message_text.insert(tk.END, content, "thinking")
            self.message_text.config(state=tk.DISABLED)
            self.message_text.see(tk.END)
    
    def _append_assistant_content(self, content: str):
        """追加助手回复内容"""
        if content is not None:
            self.message_text.config(state=tk.NORMAL)
            self.message_text.insert(tk.END, content, "assistant")
            self.message_text.config(state=tk.DISABLED)
            self.message_text.see(tk.END)
    
    def _display_message(self, role: str, content: str):
        """显示消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        self.message_text.config(state=tk.NORMAL)

        if role == "user":
            self.message_text.insert(tk.END, f"\n玩家 [{timestamp}]:\n", "user")
            self.message_text.insert(tk.END, f"{content}\n", "user")
        elif role == "assistant":
            self.message_text.insert(tk.END, f"\n游戏管理 [{timestamp}]:\n", "assistant")
            # 历史记录中只包含正文内容，直接显示
            self.message_text.insert(tk.END, f"{content}\n", "assistant")

        self.message_text.config(state=tk.DISABLED)
        self.message_text.see(tk.END)

    def _parse_plot_output_content(self, system_prompt_content: str) -> str:
        """
        从系统提示词中解析剧情必要输出内容

        Args:
            system_prompt_content: 系统提示词内容

        Returns:
            str: 解析出的剧情必要输出内容（两行）
        """
        if not system_prompt_content:
            return ""

        lines = system_prompt_content.split('\n')
        plot_output_content = ""

        for i, line in enumerate(lines):
            # 查找"剧情必要输出内容："这一行
            if "剧情必要输出内容：" in line:
                # 获取当前行和下一行
                current_line = line.strip()
                next_line = ""

                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()

                # 组合两行内容
                if next_line:
                    plot_output_content = f"{current_line}\n{next_line}"
                else:
                    plot_output_content = current_line

                print(f"[DEBUG] 解析到剧情必要输出内容: {plot_output_content}")
                break

        return plot_output_content

    def _display_cache_usage(self, cache_usage):
        """显示缓存使用情况"""
        if not cache_usage:
            return

        # 计算缓存命中率和成本节省
        hit_rate = cache_usage.cache_hit_rate * 100
        cost_savings = cache_usage.cost_savings

        # 构建缓存信息文本
        cache_info = f"💾 缓存: 命中{cache_usage.prompt_cache_hit_tokens}tokens, 未命中{cache_usage.prompt_cache_miss_tokens}tokens"
        if hit_rate > 0:
            cache_info += f", 命中率{hit_rate:.1f}%"
        if cost_savings > 0:
            cache_info += f", 节省¥{cost_savings:.4f}"

        # 显示在消息区域
        self.message_text.config(state=tk.NORMAL)
        self.message_text.insert(tk.END, f"{cache_info}\n", "system")
        self.message_text.config(state=tk.DISABLED)
        self.message_text.see(tk.END)

        # 更新状态栏
        if hit_rate > 0:
            self.status_var.set(f"缓存命中率: {hit_rate:.1f}% | 节省成本: ¥{cost_savings:.4f}")

    def _parse_and_show_quick_replies(self, content: str):
        """解析并显示快捷回复按钮 - 改进版本，支持多JSON对象解析和游戏结束检测"""
        print(f"[DEBUG] _parse_and_show_quick_replies 被调用")
        print(f"[DEBUG] 内容长度: {len(content)} 字符")
        print(f"[DEBUG] 内容预览: {content[:300]}...")

        try:
            # 优先检测游戏结束标记
            if self._check_game_over(content):
                print("[DEBUG] 检测到游戏结束标记，停止快捷回复解析")
                self._handle_game_over()
                return

            # 检测触发关键聊天剧情
            if self._check_dialogue_trigger(content):
                print("[DEBUG] 检测到触发关键聊天剧情，停止快捷回复解析")
                return

            # 检测并处理quick_input自动填入功能
            self._process_quick_input(content)

            # 首先尝试提取所有可能的JSON内容
            quick_replies = self._extract_quick_replies_from_content(content)

            if quick_replies:
                # 验证和过滤快捷回复选项
                valid_options = self._validate_quick_reply_options(quick_replies)

                if valid_options:
                    print(f"[DEBUG] 准备显示快捷回复按钮: {valid_options}")
                    self._show_quick_reply_buttons(valid_options)
                else:
                    print("[DEBUG] 没有有效的快捷回复选项")
                    self._hide_quick_reply_buttons()
            else:
                print("[DEBUG] 未找到快捷回复数据")
                self._hide_quick_reply_buttons()

        except Exception as e:
            print(f"[ERROR] 解析快捷回复失败: {e}")
            import traceback
            traceback.print_exc()
            self._hide_quick_reply_buttons()

    def _process_quick_input(self, content: str):
        """处理quick_input自动填入功能"""
        try:
            print(f"[DEBUG] 开始处理quick_input自动填入")

            # 提取quick_input内容
            quick_input_text = self._extract_quick_input_from_content(content)

            if quick_input_text:
                print(f"[DEBUG] 检测到quick_input: {quick_input_text}")
                # 自动填入输入框
                self._auto_fill_input(quick_input_text)
            else:
                print(f"[DEBUG] 未检测到quick_input内容")

        except Exception as e:
            print(f"[ERROR] 处理quick_input失败: {e}")
            import traceback
            traceback.print_exc()

    def _extract_quick_input_from_content(self, content: str) -> str:
        """从内容中提取quick_input字段值"""
        try:
            # 匹配 {"quick_input":"..."} 格式的JSON
            patterns = [
                # 在代码块中的格式
                r'```(?:json)?\s*\{\s*"quick_input"\s*:\s*"([^"]+)"\s*\}\s*```',
                # 直接的JSON格式（不在代码块中）
                r'\{\s*"quick_input"\s*:\s*"([^"]+)"\s*\}',
                # 更宽松的匹配，支持其他字段
                r'```(?:json)?\s*\{[^{}]*"quick_input"\s*:\s*"([^"]+)"[^{}]*\}\s*```',
                r'\{[^{}]*"quick_input"\s*:\s*"([^"]+)"[^{}]*\}',
            ]

            for pattern in patterns:
                matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
                if matches:
                    # 取最后一个匹配（最新的）
                    quick_input_value = matches[-1].strip()
                    print(f"[DEBUG] 成功提取quick_input: {quick_input_value}")
                    return quick_input_value

            return ""

        except Exception as e:
            print(f"[ERROR] 提取quick_input失败: {e}")
            return ""

    def _auto_fill_input(self, text: str):
        """自动填入输入框并定位光标"""
        try:
            # 清空输入框
            self.input_text.delete(1.0, tk.END)

            # 填入文本
            self.input_text.insert(1.0, text)

            # 查找下划线占位符位置
            if "___" in text:
                # 找到下划线的位置
                placeholder_pos = text.find("___")

                # 计算行列位置（Tkinter Text控件使用行.列格式）
                lines_before = text[:placeholder_pos].count('\n')
                if lines_before == 0:
                    col_pos = placeholder_pos
                else:
                    last_newline = text[:placeholder_pos].rfind('\n')
                    col_pos = placeholder_pos - last_newline - 1

                # 设置光标位置到下划线开始处
                cursor_pos = f"{lines_before + 1}.{col_pos}"
                self.input_text.mark_set(tk.INSERT, cursor_pos)

                # 选中下划线占位符，方便用户直接输入替换
                end_pos = f"{lines_before + 1}.{col_pos + 3}"  # 3是"___"的长度
                self.input_text.tag_add(tk.SEL, cursor_pos, end_pos)

                print(f"[DEBUG] 光标定位到下划线位置: {cursor_pos}")
            else:
                # 如果没有下划线，将光标定位到文本末尾
                self.input_text.mark_set(tk.INSERT, tk.END)
                print(f"[DEBUG] 光标定位到文本末尾")

            # 聚焦到输入框
            self.input_text.focus_set()

            print(f"[DEBUG] 自动填入完成: {text}")

        except Exception as e:
            print(f"[ERROR] 自动填入输入框失败: {e}")
            import traceback
            traceback.print_exc()

    def _check_game_over(self, content: str) -> bool:
        """检测AI响应中是否包含游戏结束标记"""
        if not content:
            return False

        # 检查是否包含游戏结束标记
        game_over_markers = [
            '{gameover}',
            '{GAMEOVER}',
            '{GameOver}',
            '{game_over}',
            '{GAME_OVER}'
        ]

        for marker in game_over_markers:
            if marker in content:
                print(f"[DEBUG] 检测到游戏结束标记: {marker}")
                return True

        return False

    def _handle_game_over(self):
        """处理游戏结束状态"""
        print("[DEBUG] 进入游戏结束处理流程")

        # 隐藏所有快捷回复按钮
        self._hide_quick_reply_buttons()

        # 更新状态栏
        self.status_var.set("游戏已结束")

        # 在消息区域显示游戏结束提示
        self._display_game_over_message()

        # 可选：禁用输入区域（防止用户继续输入）
        # self._disable_input_area()

        print("[DEBUG] 游戏结束处理完成")

    def _display_game_over_message(self):
        """在消息区域显示游戏结束提示"""
        try:
            self.message_text.config(state=tk.NORMAL)

            # 添加分隔线
            self.message_text.insert(tk.END, "\n" + "="*50 + "\n", "system")

            # 添加游戏结束提示
            game_over_text = "🎮 游戏已结束 🎮\n\n感谢您的游玩！如需开始新游戏，请点击\"新建会话\"按钮。\n"
            self.message_text.insert(tk.END, game_over_text, "system")

            # 添加分隔线
            self.message_text.insert(tk.END, "="*50 + "\n", "system")

            self.message_text.config(state=tk.DISABLED)
            self.message_text.see(tk.END)

            print("[DEBUG] 游戏结束消息已显示")

        except Exception as e:
            print(f"[ERROR] 显示游戏结束消息失败: {e}")

    def _extract_quick_replies_from_content(self, content: str) -> list:
        """从内容中提取快捷回复选项"""
        print(f"[DEBUG] 开始提取快捷回复内容")

        # 方法1: 优先查找 {"quick_replies": [...]} 格式
        quick_replies = self._extract_quick_replies_object_format(content)
        if quick_replies:
            print(f"[DEBUG] 通过对象格式提取成功: {len(quick_replies)} 个选项")
            return quick_replies

        # 方法2: 查找直接的JSON数组格式（向后兼容）
        quick_replies = self._extract_quick_replies_array_format(content)
        if quick_replies:
            print(f"[DEBUG] 通过数组格式提取成功: {len(quick_replies)} 个选项")
            return quick_replies

        # 方法3: 逐行解析多个JSON对象
        quick_replies = self._extract_quick_replies_multiline_json(content)
        if quick_replies:
            print(f"[DEBUG] 通过多行JSON解析成功: {len(quick_replies)} 个选项")
            return quick_replies

        print(f"[DEBUG] 所有提取方法都失败")
        return []

    def _extract_quick_replies_object_format(self, content: str) -> list:
        """提取 {"quick_replies": [...]} 格式的快捷回复"""
        patterns = [
            # 在代码块中的对象格式
            r'```(?:json)?\s*(\{[^{}]*"quick_replies"\s*:\s*\[[^\]]*\][^{}]*\})\s*```',
            # 直接的对象格式（不在代码块中）
            r'(\{[^{}]*"quick_replies"\s*:\s*\[[^\]]*\][^{}]*\})',
            # 更宽松的匹配，支持嵌套结构
            r'```(?:json)?\s*(\{.*?"quick_replies"\s*:\s*\[.*?\].*?\})\s*```',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    json_str = match.strip()
                    print(f"[DEBUG] 尝试解析对象格式JSON: {json_str[:100]}...")

                    parsed_data = json.loads(json_str)
                    if isinstance(parsed_data, dict) and "quick_replies" in parsed_data:
                        quick_replies = parsed_data["quick_replies"]
                        if isinstance(quick_replies, list) and len(quick_replies) > 0:
                            print(f"[DEBUG] 成功解析对象格式: {quick_replies}")
                            return quick_replies

                except json.JSONDecodeError as e:
                    print(f"[DEBUG] 对象格式JSON解析失败: {e}")
                    continue
                except Exception as e:
                    print(f"[DEBUG] 对象格式处理异常: {e}")
                    continue

        return []

    def _extract_quick_replies_array_format(self, content: str) -> list:
        """提取直接的JSON数组格式的快捷回复（向后兼容）"""
        patterns = [
            # 在代码块中的数组格式
            r'```(?:json)?\s*(\[[^\]]*"[^"]*"[^\]]*\])\s*```',
            # 直接的数组格式
            r'(\[(?:\s*"[^"]*"\s*,?\s*)+\])',
            # 更严格的数组格式匹配
            r'```(?:json)?\s*(\[(?:\s*"[^"]*"\s*,?\s*)*\])\s*```',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for match in matches:
                try:
                    json_str = match.strip()
                    print(f"[DEBUG] 尝试解析数组格式JSON: {json_str[:100]}...")

                    parsed_data = json.loads(json_str)
                    if isinstance(parsed_data, list) and len(parsed_data) > 0:
                        # 检查是否是有效的快捷回复数组（不是角色名+数字格式）
                        if self._is_valid_quick_reply_array(parsed_data):
                            print(f"[DEBUG] 成功解析数组格式: {parsed_data}")
                            return parsed_data
                        else:
                            print(f"[DEBUG] 数组格式无效（可能是其他数据）: {parsed_data}")

                except json.JSONDecodeError as e:
                    print(f"[DEBUG] 数组格式JSON解析失败: {e}")
                    continue
                except Exception as e:
                    print(f"[DEBUG] 数组格式处理异常: {e}")
                    continue

        return []

    def _extract_quick_replies_multiline_json(self, content: str) -> list:
        """逐行解析多个JSON对象，查找快捷回复"""
        print(f"[DEBUG] 开始多行JSON解析")

        # 分割内容为行
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # 查找包含 quick_replies 的行
            if 'quick_replies' in line.lower():
                print(f"[DEBUG] 在第{i+1}行找到quick_replies: {line[:100]}...")

                # 尝试解析这一行
                quick_replies = self._try_parse_json_line(line)
                if quick_replies:
                    return quick_replies

                # 如果单行解析失败，尝试多行组合
                quick_replies = self._try_parse_multiline_json(lines, i)
                if quick_replies:
                    return quick_replies

        return []

    def _try_parse_json_line(self, line: str) -> list:
        """尝试解析单行JSON"""
        # 移除可能的markdown标记
        line = re.sub(r'^```(?:json)?|```$', '', line).strip()

        try:
            parsed_data = json.loads(line)
            if isinstance(parsed_data, dict) and "quick_replies" in parsed_data:
                quick_replies = parsed_data["quick_replies"]
                if isinstance(quick_replies, list) and len(quick_replies) > 0:
                    print(f"[DEBUG] 单行JSON解析成功: {quick_replies}")
                    return quick_replies
        except json.JSONDecodeError:
            pass
        except Exception as e:
            print(f"[DEBUG] 单行JSON解析异常: {e}")

        return []

    def _try_parse_multiline_json(self, lines: list, start_index: int) -> list:
        """尝试解析多行JSON对象"""
        # 从当前行开始，尝试组合多行形成完整的JSON
        json_lines = []
        brace_count = 0
        in_json = False

        for i in range(start_index, min(start_index + 10, len(lines))):  # 最多向后查找10行
            line = lines[i].strip()
            if not line:
                continue

            # 移除markdown标记
            line = re.sub(r'^```(?:json)?|```$', '', line).strip()
            if not line:
                continue

            json_lines.append(line)

            # 计算大括号数量
            brace_count += line.count('{') - line.count('}')

            if '{' in line:
                in_json = True

            # 如果大括号平衡且已经开始JSON，尝试解析
            if in_json and brace_count == 0:
                json_str = ' '.join(json_lines)
                try:
                    parsed_data = json.loads(json_str)
                    if isinstance(parsed_data, dict) and "quick_replies" in parsed_data:
                        quick_replies = parsed_data["quick_replies"]
                        if isinstance(quick_replies, list) and len(quick_replies) > 0:
                            print(f"[DEBUG] 多行JSON解析成功: {quick_replies}")
                            return quick_replies
                except json.JSONDecodeError:
                    pass
                break

        return []

    def _is_valid_quick_reply_array(self, data: list) -> bool:
        """检查数组是否是有效的快捷回复选项"""
        if not data or len(data) == 0:
            return False

        # 检查每个选项
        for item in data:
            if not isinstance(item, str):
                return False

            item = item.strip()
            if not item:
                return False

            # 过滤掉角色名+数字格式（如"苏瑶100", "张轩0"）
            if re.match(r'^[\u4e00-\u9fff]+\d+$', item):
                print(f"[DEBUG] 检测到角色名+数字格式，跳过: {item}")
                return False

            # 过滤掉纯数字
            if item.isdigit():
                print(f"[DEBUG] 检测到纯数字，跳过: {item}")
                return False

            # 过滤掉过短的选项（少于3个字符）
            if len(item) < 3:
                print(f"[DEBUG] 选项过短，跳过: {item}")
                return False

        return True

    def _validate_quick_reply_options(self, options: list) -> list:
        """验证和过滤快捷回复选项"""
        if not options:
            return []

        valid_options = []

        for option in options:
            if not isinstance(option, str):
                continue

            option = option.strip()
            if not option:
                continue

            # 过滤无效格式
            if re.match(r'^[\u4e00-\u9fff]+\d+$', option):  # 角色名+数字
                print(f"[DEBUG] 过滤角色名+数字格式: {option}")
                continue

            if option.isdigit():  # 纯数字
                print(f"[DEBUG] 过滤纯数字: {option}")
                continue

            if len(option) < 3:  # 过短
                print(f"[DEBUG] 过滤过短选项: {option}")
                continue

            if len(option) > 50:  # 过长（调整为50个字符）
                print(f"[DEBUG] 过滤过长选项: {option[:30]}...")
                continue

            # 检查是否包含有意义的内容（至少包含一个中文字符或多个英文单词）
            if not (re.search(r'[\u4e00-\u9fff]', option) or len(option.split()) >= 2):
                print(f"[DEBUG] 过滤无意义选项: {option}")
                continue

            valid_options.append(option)

        # 限制选项数量（最多6个）
        if len(valid_options) > 6:
            valid_options = valid_options[:6]
            print(f"[DEBUG] 限制选项数量为6个")

        print(f"[DEBUG] 验证后的有效选项: {valid_options}")
        return valid_options

    def _show_quick_reply_buttons(self, options: List[str]):
        """显示优化的快捷回复按钮"""
        print(f"[DEBUG] _show_quick_reply_buttons 被调用，选项: {options}")

        # 清除之前的按钮
        self._hide_quick_reply_buttons()

        # 选项已经在 _validate_quick_reply_options 中验证过了，这里直接使用
        if not options:
            print("[DEBUG] 没有选项，返回")
            return

        # 显示快捷回复框架 - 确保在输入区域之前显示
        print("[DEBUG] 显示快捷回复框架")
        print(f"[DEBUG] 输入框架: {self.input_frame}")
        self.quick_reply_frame.pack(fill=tk.X, pady=(5, 5), before=self.input_frame)

        # 添加标签
        label = ttk.Label(self.quick_reply_frame, text="快捷回复:")
        label.pack(side=tk.LEFT, padx=(0, 10))
        self.quick_reply_buttons.append(label)

        # 创建按钮容器框架，支持换行显示
        button_container = ttk.Frame(self.quick_reply_frame)
        button_container.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.quick_reply_buttons.append(button_container)

        # 当前行的框架
        current_row_frame = ttk.Frame(button_container)
        current_row_frame.pack(fill=tk.X, pady=(0, 2))
        self.quick_reply_buttons.append(current_row_frame)

        current_row_width = 0
        max_row_width = 600  # 每行最大宽度（像素）

        # 创建优化的按钮
        for i, option in enumerate(options):
            print(f"[DEBUG] 创建按钮 {i+1}: {option}")

            # 处理文本显示
            original_text = option
            display_text = self._truncate_text(option, self.button_max_text_length)

            # 计算按钮字符宽度
            button_width_chars = self._calculate_button_width_chars(display_text)

            # 估算按钮像素宽度用于换行判断（每个字符约8像素）
            estimated_width_px = button_width_chars * 8

            # 检查是否需要换行
            if current_row_width + estimated_width_px > max_row_width and current_row_width > 0:
                # 创建新行
                current_row_frame = ttk.Frame(button_container)
                current_row_frame.pack(fill=tk.X, pady=(0, 2))
                self.quick_reply_buttons.append(current_row_frame)
                current_row_width = 0

            # 创建按钮
            btn = ttk.Button(
                current_row_frame,
                text=display_text,
                command=lambda opt=original_text: self._quick_reply_clicked(opt),
                width=button_width_chars  # 使用计算出的字符宽度
            )

            btn.pack(side=tk.LEFT, padx=(0, 5), pady=(0, 2))
            self.quick_reply_buttons.append(btn)

            # 添加工具提示（只有当文本被截断时才添加）
            if len(original_text) > self.button_max_text_length:
                ToolTip(btn, original_text)
                print(f"[DEBUG] 为按钮 {i+1} 添加工具提示: {original_text}")

            # 更新当前行宽度
            current_row_width += estimated_width_px + 5  # 5像素间距

            print(f"[DEBUG] 按钮 {i+1} 创建完成: 显示文本='{display_text}', 字符宽度={button_width_chars}, 原始文本='{original_text}'")

        print(f"[DEBUG] 所有按钮创建完成，总数: {len(self.quick_reply_buttons)}")

        # 检查是否需要触发自动选择
        self._check_auto_select_trigger()

    def _check_auto_select_trigger(self):
        """检查是否需要触发自动选择"""
        try:
            # 只有在启用自动选择且不在自动执行模式下才触发单独的自动选择
            if (self.auto_select_enabled.get() and
                not self.auto_execute_enabled.get() and
                self.quick_reply_buttons):

                print("[自动测试] 检测到快捷回复选项，准备自动选择")
                delay_ms = self.auto_select_delay.get() * 1000
                self.root.after(delay_ms, self._perform_auto_select)

        except Exception as e:
            print(f"[自动测试] 检查自动选择触发时出错: {e}")

    def _hide_quick_reply_buttons(self):
        """隐藏快捷回复按钮"""
        print(f"[DEBUG] _hide_quick_reply_buttons 被调用，当前按钮数量: {len(self.quick_reply_buttons)}")
        # 销毁所有控件（包括按钮和标签）
        for widget in self.quick_reply_buttons:
            widget.destroy()
        self.quick_reply_buttons.clear()

        # 隐藏框架
        self.quick_reply_frame.pack_forget()
        print(f"[DEBUG] 快捷回复按钮已隐藏")

    def _quick_reply_clicked(self, option: str):
        """快捷回复按钮点击事件"""
        # 构建最终的消息内容
        final_message = option

        # 如果启用了自动Function Calling，追加调用指令
        if self.auto_call_function_calling.get():
            final_message += "\n\n调用Function Calling显示游戏数据"
            print(f"[DEBUG] 已启用自动Function Calling，追加调用指令")

        # 将最终内容填入输入框
        self.input_text.delete(1.0, tk.END)
        self.input_text.insert(1.0, final_message)

        # 自动发送，并标记为快捷回复触发
        self._send_message(is_quick_reply=True)

    def _handle_api_error(self, error_msg: str):
        """处理API错误"""
        self.message_text.config(state=tk.NORMAL)
        self.message_text.insert(tk.END, f"\n[错误] {error_msg}\n", "system")
        self.message_text.config(state=tk.DISABLED)
        self.message_text.see(tk.END)

        self.status_var.set(f"错误: {error_msg}")
        messagebox.showerror("API错误", error_msg)

    def _finish_sending(self):
        """完成发送，恢复界面状态"""
        self.is_sending = False
        self.send_button.config(state=tk.NORMAL)
        self.status_var.set("就绪")

        # 重新获得焦点
        self.input_text.focus_set()

    def _toggle_function_calling(self):
        """切换Function Calling功能"""
        try:
            if self.function_calling_var.get():
                self.api_client.enable_function_calling()
                self.status_var.set("Function Calling已启用")
                print("[Function Calling] 已启用")
            else:
                self.api_client.disable_function_calling()
                self.status_var.set("Function Calling已禁用")
                print("[Function Calling] 已禁用")
        except Exception as e:
            print(f"[Function Calling] 切换失败: {e}")
            messagebox.showerror("错误", f"切换Function Calling失败: {str(e)}")

    def _display_function_call(self, function_name: str, result: str):
        """显示函数调用过程"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            function_display = f"🔧 调用函数: {function_name}() → {result}"

            self.message_text.config(state=tk.NORMAL)
            self.message_text.insert(tk.END, f"\n[{timestamp}] ", "timestamp")
            self.message_text.insert(tk.END, function_display + "\n", "function_call")
            self.message_text.config(state=tk.DISABLED)
            self.message_text.see(tk.END)
        except Exception as e:
            print(f"[Function Calling] 显示函数调用失败: {e}")

    def _display_function_call_message(self, content: str):
        """显示函数调用消息"""
        try:
            self.message_text.config(state=tk.NORMAL)
            self.message_text.insert(tk.END, content + "\n", "function_call")
            self.message_text.config(state=tk.DISABLED)
            self.message_text.see(tk.END)
        except Exception as e:
            print(f"[Function Calling] 显示函数调用消息失败: {e}")

    def _on_closing(self):
        """窗口关闭事件"""
        if self.is_sending:
            if not messagebox.askyesno("确认", "正在发送消息，确定要关闭吗？"):
                return

        self.root.destroy()

    def _check_dialogue_trigger(self, content: str) -> bool:
        """
        检测是否触发关键聊天剧情

        Args:
            content: AI响应内容

        Returns:
            bool: 是否检测到触发条件
        """
        print(f"[DEBUG] 开始检测触发关键聊天剧情")

        # 改进的正则表达式，使用非贪婪匹配和更精确的模式
        patterns = [
            # 匹配完整的JSON对象，使用递归匹配大括号
            r'\{"触发关键聊天剧情"\s*:\s*(\[(?:[^\[\]]|(?:\[[^\[\]]*\]))*\])\}',
            # 更宽松的匹配，处理复杂的嵌套结构
            r'\{[^{}]*?"触发关键聊天剧情"\s*:\s*(\[.*?\])[^{}]*?\}',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    print(f"[DEBUG] 尝试解析匹配内容: {match[:200]}...")
                    # 解析JSON数组
                    npc_data = json.loads(match)
                    if isinstance(npc_data, list) and len(npc_data) >= 6:
                        # 兼容6个或7个元素的数据格式
                        if len(npc_data) == 6:
                            # 如果只有6个元素，添加空字符串作为第7个元素
                            npc_data.append("")
                            print(f"[DEBUG] 兼容6元素格式，自动添加第7个元素: NPC={npc_data[0]}")
                        else:
                            print(f"[DEBUG] 成功解析触发数据: NPC={npc_data[0]}")
                        self._trigger_dialogue(npc_data)
                        return True
                    else:
                        print(f"[DEBUG] 触发数据格式错误，期望6-7个元素，实际{len(npc_data)}个")
                except json.JSONDecodeError as e:
                    print(f"[DEBUG] 解析触发数据JSON失败: {e}")
                    # 尝试修复常见的JSON格式问题
                    fixed_match = self._try_fix_json(match)
                    if fixed_match:
                        try:
                            npc_data = json.loads(fixed_match)
                            if isinstance(npc_data, list) and len(npc_data) >= 6:
                                # 兼容6个或7个元素的数据格式
                                if len(npc_data) == 6:
                                    npc_data.append("")
                                    print(f"[DEBUG] 修复后兼容6元素格式: NPC={npc_data[0]}")
                                else:
                                    print(f"[DEBUG] 修复后成功解析触发数据: NPC={npc_data[0]}")
                                self._trigger_dialogue(npc_data)
                                return True
                        except:
                            # 如果修复后仍然失败，尝试手动解析数组
                            npc_data = self._parse_array_manually(fixed_match)
                            if npc_data and len(npc_data) >= 6:
                                # 兼容6个或7个元素的数据格式
                                if len(npc_data) == 6:
                                    npc_data.append("")
                                    print(f"[DEBUG] 手动解析数组兼容6元素格式: NPC={npc_data[0]}")
                                else:
                                    print(f"[DEBUG] 手动解析数组成功: NPC={npc_data[0]}")
                                self._trigger_dialogue(npc_data)
                                return True
                            continue
                    continue
                except Exception as e:
                    print(f"[DEBUG] 处理触发数据异常: {e}")
                    continue

        # 如果正则匹配失败，尝试手动查找和解析
        return self._manual_trigger_detection(content)

    def _try_fix_json(self, json_str: str) -> str:
        """尝试修复常见的JSON格式问题"""
        try:
            # 移除可能的markdown标记
            json_str = re.sub(r'^```(?:json)?|```$', '', json_str.strip())

            # 修复中文标点符号问题
            # 将中文冒号替换为英文冒号（但只在字符串内部）
            # 这是一个复杂的问题，需要小心处理

            # 简单的修复方法：如果JSON解析失败，尝试直接提取数组部分
            # 查找数组的开始和结束
            array_start = json_str.find('[')
            array_end = json_str.rfind(']')

            if array_start != -1 and array_end != -1 and array_end > array_start:
                array_content = json_str[array_start:array_end + 1]
                print(f"[DEBUG] 尝试提取数组部分: {array_content[:100]}...")
                return array_content

            return json_str
        except:
            return None

    def _parse_array_manually(self, array_str: str) -> List[str]:
        """手动解析JSON数组，处理包含中文标点的情况"""
        try:
            if not array_str.strip().startswith('[') or not array_str.strip().endswith(']'):
                return None

            # 移除首尾的方括号
            content = array_str.strip()[1:-1]

            # 手动分割数组元素
            elements = []
            current_element = ""
            in_quotes = False
            escape_next = False
            quote_count = 0

            i = 0
            while i < len(content):
                char = content[i]

                if escape_next:
                    current_element += char
                    escape_next = False
                elif char == '\\':
                    current_element += char
                    escape_next = True
                elif char == '"':
                    current_element += char
                    if not escape_next:
                        in_quotes = not in_quotes
                        if in_quotes:
                            quote_count += 1
                elif char == ',' and not in_quotes:
                    # 找到元素分隔符
                    element = current_element.strip()
                    if element.startswith('"') and element.endswith('"'):
                        element = element[1:-1]  # 移除引号
                    elements.append(element)
                    current_element = ""
                else:
                    current_element += char

                i += 1

            # 添加最后一个元素
            if current_element.strip():
                element = current_element.strip()
                if element.startswith('"') and element.endswith('"'):
                    element = element[1:-1]  # 移除引号
                elements.append(element)

            print(f"[DEBUG] 手动解析得到{len(elements)}个元素")
            return elements if len(elements) == 7 else None

        except Exception as e:
            print(f"[DEBUG] 手动解析数组失败: {e}")
            return None

    def _manual_trigger_detection(self, content: str) -> bool:
        """手动检测触发模式，作为正则匹配的备选方案"""
        try:
            # 查找"触发关键聊天剧情"关键字
            if "触发关键聊天剧情" not in content:
                return False

            print(f"[DEBUG] 发现触发关键字，尝试手动解析")

            # 查找JSON对象的开始和结束位置
            start_pos = content.find('{"触发关键聊天剧情"')
            if start_pos == -1:
                return False

            # 从开始位置查找完整的JSON对象
            brace_count = 0
            end_pos = start_pos
            in_string = False
            escape_next = False

            for i, char in enumerate(content[start_pos:], start_pos):
                if escape_next:
                    escape_next = False
                    continue

                if char == '\\':
                    escape_next = True
                    continue

                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue

                if not in_string:
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_pos = i + 1
                            break

            if brace_count == 0:
                json_str = content[start_pos:end_pos]
                print(f"[DEBUG] 手动提取的JSON: {json_str[:200]}...")

                try:
                    parsed_data = json.loads(json_str)
                    if "触发关键聊天剧情" in parsed_data:
                        npc_data = parsed_data["触发关键聊天剧情"]
                        if isinstance(npc_data, list) and len(npc_data) >= 6:
                            # 兼容6个或7个元素的数据格式
                            if len(npc_data) == 6:
                                npc_data.append("")
                                print(f"[DEBUG] 手动解析兼容6元素格式: NPC={npc_data[0]}")
                            else:
                                print(f"[DEBUG] 手动解析成功: NPC={npc_data[0]}")
                            self._trigger_dialogue(npc_data)
                            return True
                        else:
                            print(f"[DEBUG] 手动解析数据格式错误，期望6-7个元素，实际{len(npc_data)}个")
                except json.JSONDecodeError as e:
                    print(f"[DEBUG] 手动解析JSON失败: {e}")

            return False

        except Exception as e:
            print(f"[DEBUG] 手动检测异常: {e}")
            return False

    def _trigger_dialogue(self, npc_data: List[str]):
        """
        触发对话窗口

        Args:
            npc_data: 包含7个元素的NPC数据数组
        """
        try:
            print(f"[DEBUG] 触发对话窗口 - NPC: {npc_data[0]}")

            # 创建对话窗口
            dialogue_window = DialogueWindow(
                parent_gui=self,
                npc_data=npc_data,
                on_dialogue_complete=self._on_dialogue_complete
            )

            # 显示对话窗口
            dialogue_window.show()

        except Exception as e:
            print(f"[ERROR] 创建对话窗口失败: {e}")
            messagebox.showerror("对话窗口错误", f"无法创建对话窗口:\n{str(e)}")

    def _on_dialogue_complete(self, dialogue_history: str, npc_name: str = None):
        """
        对话完成回调函数 - 支持完成和未完成的对话

        Args:
            dialogue_history: 完整的对话历史记录（包括未完成的对话）
            npc_name: NPC名字，用于生成衔接提示
        """
        try:
            print(f"[DEBUG] 对话完成回调被调用")
            print(f"[DEBUG] - 历史记录长度: {len(dialogue_history)}")
            print(f"[DEBUG] - NPC名字: {npc_name}")

            # 检测是否为未完成的对话
            is_incomplete = "未完成" in dialogue_history or "提前结束" in dialogue_history
            print(f"[DEBUG] - 对话类型: {'未完成对话' if is_incomplete else '完成对话'}")

            # 将对话历史添加到主聊天记录
            self.history_manager.add_message("assistant", dialogue_history)

            # 在主窗口显示对话历史
            self._display_message("assistant", dialogue_history)

            print("[DEBUG] 对话历史已添加到主聊天")

            # 根据对话完成状态生成不同的衔接提示
            if is_incomplete:
                print(f"[DEBUG] 处理未完成对话的主线剧情衔接...")
                self._trigger_incomplete_dialogue_continuation(npc_name or "NPC")
            else:
                print(f"[DEBUG] 处理完成对话的主线剧情衔接...")
                self._trigger_main_story_continuation(npc_name or "NPC")

        except Exception as e:
            print(f"[ERROR] 处理对话完成回调失败: {e}")
            import traceback
            traceback.print_exc()

    def _trigger_incomplete_dialogue_continuation(self, npc_name: str):
        """
        触发未完成对话的主线剧情衔接

        Args:
            npc_name: 未完成对话的NPC名字
        """
        try:
            print(f"[DEBUG] _trigger_incomplete_dialogue_continuation 被调用")
            print(f"[DEBUG] - NPC名字: {npc_name}")

            # 检查当前状态
            print(f"[DEBUG] - 当前发送状态: {getattr(self, 'is_sending', 'undefined')}")
            print(f"[DEBUG] - API客户端状态: {self.api_client is not None}")

            # 构建未完成对话的衔接提示词
            continuation_prompt = f"玩家与【{npc_name}】的单独对话提前结束了，请基于已进行的对话内容继续主线剧情的发展。"
            print(f"[DEBUG] - 未完成对话衔接提示词: {continuation_prompt}")

            # 检查是否正在发送中
            if getattr(self, 'is_sending', False):
                print("[WARNING] 当前正在发送中，延迟触发未完成对话衔接")
                # 使用安全的延迟执行
                try:
                    self.root.after(1000, lambda: self._trigger_incomplete_dialogue_continuation(npc_name))
                except RuntimeError as e:
                    if "main thread is not in main loop" in str(e):
                        print("[WARNING] 主循环未运行，直接执行未完成对话衔接")
                        self._send_continuation_api_request_direct(continuation_prompt)
                    else:
                        raise e
                return

            print("[DEBUG] 创建未完成对话衔接API请求线程...")
            # 在新线程中发送衔接API请求
            thread = threading.Thread(
                target=self._send_continuation_api_request,
                args=(continuation_prompt,)
            )
            thread.daemon = True
            thread.start()
            print("[DEBUG] 未完成对话衔接API请求线程已启动")

        except Exception as e:
            print(f"[ERROR] 触发未完成对话衔接失败: {e}")
            import traceback
            traceback.print_exc()

    def _trigger_main_story_continuation(self, npc_name: str):
        """
        触发主线剧情衔接

        Args:
            npc_name: 刚完成对话的NPC名字
        """
        try:
            print(f"[DEBUG] _trigger_main_story_continuation 被调用")
            print(f"[DEBUG] - NPC名字: {npc_name}")

            # 检查当前状态
            print(f"[DEBUG] - 当前发送状态: {getattr(self, 'is_sending', 'undefined')}")
            print(f"[DEBUG] - API客户端状态: {self.api_client is not None}")

            # 构建衔接提示词
            continuation_prompt = f"玩家与【{npc_name}】的单独对话已结束，请继续主线剧情的发展。"
            print(f"[DEBUG] - 衔接提示词: {continuation_prompt}")

            # 检查是否正在发送中
            if getattr(self, 'is_sending', False):
                print("[WARNING] 当前正在发送中，延迟触发主线剧情衔接")
                # 使用安全的延迟执行
                try:
                    self.root.after(1000, lambda: self._trigger_main_story_continuation(npc_name))
                except RuntimeError as e:
                    if "main thread is not in main loop" in str(e):
                        print("[WARNING] 主循环未运行，直接执行衔接")
                        self._send_continuation_api_request_direct(continuation_prompt)
                    else:
                        raise e
                return

            print("[DEBUG] 创建衔接API请求线程...")
            # 在新线程中发送衔接API请求
            thread = threading.Thread(
                target=self._send_continuation_api_request,
                args=(continuation_prompt,)
            )
            thread.daemon = True
            thread.start()
            print("[DEBUG] 衔接API请求线程已启动")

        except Exception as e:
            print(f"[ERROR] 触发主线剧情衔接失败: {e}")
            import traceback
            traceback.print_exc()

    def _send_continuation_api_request(self, continuation_prompt: str):
        """
        在后台线程中发送主线剧情衔接API请求

        Args:
            continuation_prompt: 衔接提示词
        """
        try:
            print(f"[DEBUG] _send_continuation_api_request 开始执行")
            print(f"[DEBUG] - 衔接提示词: {continuation_prompt}")

            # 检查主循环是否运行
            def safe_after(func):
                """安全的after调用"""
                try:
                    self.root.after(0, func)
                except RuntimeError as e:
                    if "main thread is not in main loop" in str(e):
                        print(f"[WARNING] 主循环未运行，直接执行: {func}")
                        try:
                            func()
                        except Exception as direct_e:
                            print(f"[ERROR] 直接执行失败: {direct_e}")
                    else:
                        raise e

            # 设置发送状态
            safe_after(lambda: setattr(self, 'is_sending', True))

            # 更新状态
            safe_after(lambda: self.status_var.set("主线剧情衔接中..."))
            print("[DEBUG] 状态已更新为：主线剧情衔接中...")

            # 构建消息历史
            messages = self.history_manager.get_context_messages()
            print(f"[DEBUG] 获取到历史消息数量: {len(messages)}")

            # 添加衔接提示作为用户消息
            messages.append(ChatMessage(role="user", content=continuation_prompt))
            print(f"[DEBUG] 添加衔接提示后消息数量: {len(messages)}")

            # 在主窗口显示衔接提示
            print("[DEBUG] 在主窗口显示衔接提示...")
            safe_after(lambda: self._display_message("user", continuation_prompt))
            safe_after(lambda: self.history_manager.add_message("user", continuation_prompt))

            # 准备显示AI回复
            print("[DEBUG] 准备显示AI回复...")
            safe_after(lambda: self._start_assistant_message())

            assistant_content = ""

            # 发送流式请求
            selected_model = self.current_model.get()

            # 获取thinking模式
            thinking_mode = self._get_thinking_mode_for_model(selected_model)

            print(f"[DEBUG] 主线衔接API调用:")
            print(f"  模型: {selected_model}")
            print(f"  消息数量: {len(messages)}")
            print(f"  最大令牌数: {self.max_tokens.get()}")
            print(f"  温度: {self.temperature.get()}")
            print(f"  思考模式: {thinking_mode}")

            print("[DEBUG] 开始发送API请求...")
            chunk_count = 0
            thinking_content = ""  # 添加思考内容变量

            for chunk in self.api_client.chat_completion(
                messages=messages,
                model=selected_model,
                max_tokens=self.max_tokens.get(),
                temperature=self.temperature.get(),
                stream=True,
                thinking_mode=thinking_mode
            ):
                chunk_count += 1
                if chunk_count % 10 == 0:  # 每10个chunk打印一次
                    print(f"[DEBUG] 已处理 {chunk_count} 个chunk")

                if chunk.error:
                    print(f"[ERROR] API请求出错: {chunk.error}")
                    safe_after(lambda e=chunk.error: self._handle_api_error(e))
                    return

                if chunk.is_complete:
                    print("[DEBUG] API请求完成")
                    break

                # 修复：正确处理思考内容和普通内容
                if chunk.content is not None:
                    if chunk.is_thinking:
                        thinking_content += chunk.content
                        safe_after(lambda c=chunk.content: self._append_thinking_content(c))
                    else:
                        assistant_content += chunk.content
                        safe_after(lambda c=chunk.content: self._append_assistant_content(c))

            print(f"[DEBUG] API响应接收完成，总chunk数: {chunk_count}")
            print(f"[DEBUG] 响应内容长度: {len(assistant_content)}")

            # 添加到历史记录
            print("[DEBUG] 添加响应到历史记录...")
            safe_after(lambda: self.history_manager.add_message("assistant", assistant_content))

            # 解析快捷回复
            print("[DEBUG] 解析快捷回复...")
            safe_after(lambda: self._parse_and_show_quick_replies(assistant_content))

            print(f"[DEBUG] 主线剧情衔接完成，内容长度: {len(assistant_content)}")

        except Exception as e:
            print(f"[ERROR] 主线剧情衔接API请求失败: {e}")
            import traceback
            traceback.print_exc()
            safe_after(lambda: self._handle_api_error(str(e)))
        finally:
            # 恢复状态
            print("[DEBUG] 恢复发送状态...")
            safe_after(lambda: self._finish_sending())

    def _send_continuation_api_request_direct(self, continuation_prompt: str):
        """
        直接发送主线剧情衔接API请求（不使用GUI线程同步）
        用于主循环未运行时的备选方案

        Args:
            continuation_prompt: 衔接提示词
        """
        try:
            print(f"[DEBUG] _send_continuation_api_request_direct 开始执行")
            print(f"[DEBUG] - 衔接提示词: {continuation_prompt}")

            # 直接设置状态
            self.is_sending = True

            # 构建消息历史
            messages = self.history_manager.get_context_messages()
            print(f"[DEBUG] 获取到历史消息数量: {len(messages)}")

            # 添加衔接提示作为用户消息
            messages.append(ChatMessage(role="user", content=continuation_prompt))
            print(f"[DEBUG] 添加衔接提示后消息数量: {len(messages)}")

            # 直接添加到历史记录
            self.history_manager.add_message("user", continuation_prompt)

            assistant_content = ""
            thinking_content = ""  # 添加思考内容变量

            # 发送流式请求
            selected_model = self.current_model.get()
            thinking_mode = self._get_thinking_mode_for_model(selected_model)

            print(f"[DEBUG] 直接API调用:")
            print(f"  模型: {selected_model}")
            print(f"  消息数量: {len(messages)}")
            print(f"  思考模式: {thinking_mode}")

            print("[DEBUG] 开始发送API请求...")
            for chunk in self.api_client.chat_completion(
                messages=messages,
                model=selected_model,
                max_tokens=self.max_tokens.get(),
                temperature=self.temperature.get(),
                stream=True,
                thinking_mode=thinking_mode
            ):
                if chunk.error:
                    print(f"[ERROR] API请求出错: {chunk.error}")
                    return

                if chunk.is_complete:
                    print("[DEBUG] API请求完成")
                    break

                # 修复：正确处理思考内容和普通内容
                if chunk.content is not None:
                    if chunk.is_thinking:
                        thinking_content += chunk.content
                        print(f"[DEBUG] 收到思考内容: {chunk.content[:50]}...")
                    else:
                        assistant_content += chunk.content

            print(f"[DEBUG] API响应接收完成")
            print(f"[DEBUG] 响应内容长度: {len(assistant_content)}")

            # 直接添加到历史记录
            self.history_manager.add_message("assistant", assistant_content)

            print(f"[DEBUG] 主线剧情衔接完成（直接模式），内容长度: {len(assistant_content)}")

        except Exception as e:
            print(f"[ERROR] 直接主线剧情衔接API请求失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 恢复状态
            self.is_sending = False
            print("[DEBUG] 恢复发送状态（直接模式）")

    def run(self):
        """运行GUI"""
        # 测试API连接
        connection_results = self.api_client.test_all_connections()
        failed_providers = []
        success_providers = []

        for provider, (success, message) in connection_results.items():
            if success:
                success_providers.append(f"{provider}: {message}")
            else:
                failed_providers.append(f"{provider}: {message}")

        if failed_providers and not success_providers:
            # 所有提供商都失败
            messagebox.showwarning("连接测试", f"所有API连接测试失败:\n" + "\n".join(failed_providers))
            self.status_var.set("API连接失败")
        elif failed_providers:
            # 部分提供商失败
            self.status_var.set(f"部分API可用 ({len(success_providers)}/{len(connection_results)})")
        else:
            # 所有提供商成功
            self.status_var.set("所有API连接正常")

        # 启动GUI主循环
        self.root.mainloop()

    # ==================== 自动测试功能方法 ====================

    def _on_auto_select_toggle(self):
        """自动选择开关切换事件"""
        if self.auto_select_enabled.get():
            print("[自动测试] 自动选择已启用")
            self._update_auto_test_status()
        else:
            print("[自动测试] 自动选择已禁用")
            self._update_auto_test_status()

    def _on_auto_execute_toggle(self):
        """自动执行开关切换事件"""
        if self.auto_execute_enabled.get():
            print("[自动测试] 自动执行已启用")
            self._update_auto_test_status()
        else:
            print("[自动测试] 自动执行已禁用")
            # 如果正在自动执行，停止它
            if self.auto_execute_timer_id:
                self._stop_auto_test()
            self._update_auto_test_status()

    def _update_auto_test_status(self):
        """更新自动测试状态显示"""
        auto_select = self.auto_select_enabled.get()
        auto_execute = self.auto_execute_enabled.get()

        if auto_select and auto_execute:
            status = f"就绪 ({self.current_auto_round}/{self.auto_execute_rounds.get()})"
            self.auto_test_control_btn.config(state=tk.NORMAL)
        elif auto_select:
            status = "仅自动选择"
            self.auto_test_control_btn.config(state=tk.DISABLED)
        elif auto_execute:
            status = "仅自动执行"
            self.auto_test_control_btn.config(state=tk.DISABLED)
        else:
            status = "未启用"
            self.auto_test_control_btn.config(state=tk.DISABLED)

        self.auto_test_status_label.config(text=status)

    def _toggle_auto_test(self):
        """开始/停止自动测试"""
        if self.auto_execute_timer_id:
            # 正在运行，停止测试
            self._stop_auto_test()
        else:
            # 未运行，开始测试
            self._start_auto_test()

    def _start_auto_test(self):
        """开始自动测试"""
        if not (self.auto_select_enabled.get() and self.auto_execute_enabled.get()):
            messagebox.showwarning("提示", "请同时启用自动选择和自动执行功能")
            return

        if not self.history_manager.current_session:
            messagebox.showwarning("提示", "请先创建新会话")
            return

        # 初始化日志
        self._init_auto_test_log()

        # 记录开始时间
        self.auto_test_start_time = datetime.now()

        # 重置计数器
        self.current_auto_round = 0

        # 更新按钮状态
        self.auto_test_control_btn.config(text="停止自动测试")

        # 禁用相关控件
        self.auto_select_checkbox.config(state=tk.DISABLED)
        self.auto_execute_checkbox.config(state=tk.DISABLED)

        print(f"[自动测试] 开始自动测试，目标回合数: {self.auto_execute_rounds.get()}")
        self._log_auto_test(f"开始自动测试，目标回合数: {self.auto_execute_rounds.get()}", "INFO")

        # 开始第一轮自动执行
        self._execute_auto_round()

    def _stop_auto_test(self):
        """停止自动测试"""
        if self.auto_execute_timer_id:
            self.root.after_cancel(self.auto_execute_timer_id)
            self.auto_execute_timer_id = None

        # 更新按钮状态
        self.auto_test_control_btn.config(text="开始自动测试")

        # 启用相关控件
        self.auto_select_checkbox.config(state=tk.NORMAL)
        self.auto_execute_checkbox.config(state=tk.NORMAL)

        print(f"[自动测试] 自动测试已停止，完成回合数: {self.current_auto_round}")
        self._log_auto_test(f"自动测试已停止，完成回合数: {self.current_auto_round}", "INFO")

        # 记录测试总结
        self._log_auto_test_summary()

        self._update_auto_test_status()

    def _execute_auto_round(self):
        """执行一轮自动测试"""
        try:
            # 检查是否达到目标回合数
            if self.current_auto_round >= self.auto_execute_rounds.get():
                print(f"[自动测试] 已完成所有回合 ({self.current_auto_round}/{self.auto_execute_rounds.get()})")
                self._log_auto_test(f"自动测试完成，总回合数: {self.current_auto_round}", "INFO")
                self._stop_auto_test()
                messagebox.showinfo("自动测试完成", f"已完成 {self.current_auto_round} 个回合的自动测试")
                return

            # 检查是否有快捷回复选项可用
            if not self.quick_reply_buttons:
                print("[自动测试] 没有可用的快捷回复选项，等待...")
                # 等待一段时间后重试
                self.auto_execute_timer_id = self.root.after(2000, self._execute_auto_round)
                return

            # 增加回合计数
            self.current_auto_round += 1
            print(f"[自动测试] 执行第 {self.current_auto_round} 回合")
            self._log_auto_test(f"执行第 {self.current_auto_round} 回合", "DEBUG")

            # 更新状态显示
            self._update_auto_test_status()

            # 执行自动选择
            self._perform_auto_select()

        except Exception as e:
            print(f"[自动测试] 执行自动回合时出错: {e}")
            self._log_auto_test(f"执行自动回合时出错: {e}", "ERROR")
            self._stop_auto_test()

    def _perform_auto_select(self):
        """执行自动选择"""
        try:
            if not self.quick_reply_buttons:
                print("[自动测试] 没有可用的快捷回复选项")
                return

            # 获取所有按钮的文本选项
            available_options = []
            for button in self.quick_reply_buttons:
                if hasattr(button, 'cget') and button.cget('text'):
                    available_options.append(button.cget('text'))

            if not available_options:
                print("[自动测试] 无法获取快捷回复选项")
                return

            # 随机选择一个选项
            selected_option = random.choice(available_options)
            print(f"[自动测试] 随机选择选项: {selected_option}")
            self._log_auto_test(f"随机选择选项: {selected_option}")

            # 延迟后执行选择
            delay_ms = self.auto_select_delay.get() * 1000
            self.root.after(delay_ms, lambda: self._execute_selected_option(selected_option))

        except Exception as e:
            print(f"[自动测试] 执行自动选择时出错: {e}")
            self._log_auto_test(f"执行自动选择时出错: {e}")

    def _execute_selected_option(self, option_text):
        """执行选中的选项"""
        try:
            # 找到对应的按钮并触发点击
            button_found = False
            for button in self.quick_reply_buttons:
                if hasattr(button, 'cget') and hasattr(button, 'invoke'):
                    try:
                        button_text = button.cget('text')
                        if button_text == option_text:
                            # 模拟按钮点击
                            button.invoke()
                            button_found = True
                            print(f"[自动测试] 成功点击按钮: {option_text}")
                            break
                    except tk.TclError:
                        # 按钮可能已被销毁，跳过
                        continue

            if not button_found:
                print(f"[自动测试] 未找到对应的按钮: {option_text}")
                # 尝试直接发送消息
                self._send_auto_message(option_text)

            # 安排下一轮自动执行
            if self.auto_execute_enabled.get() and self.auto_execute_timer_id is not None:
                # 等待AI响应完成后再执行下一轮
                self.root.after(5000, self._wait_for_response_and_continue)

        except Exception as e:
            print(f"[自动测试] 执行选中选项时出错: {e}")
            self._log_auto_test(f"执行选中选项时出错: {e}")

    def _send_auto_message(self, message):
        """自动发送消息（当按钮不可用时的备选方案）"""
        try:
            if self.is_sending:
                print("[自动测试] 正在发送中，跳过自动消息")
                return

            # 设置输入框内容
            self.input_text.delete(1.0, tk.END)
            self.input_text.insert(1.0, message)

            # 发送消息
            self._send_message(is_quick_reply=True)
            print(f"[自动测试] 自动发送消息: {message}")

        except Exception as e:
            print(f"[自动测试] 自动发送消息时出错: {e}")
            self._log_auto_test(f"自动发送消息时出错: {e}")

    def _wait_for_response_and_continue(self):
        """等待AI响应完成后继续下一轮"""
        try:
            # 检查是否还在发送中
            if self.is_sending:
                # 还在发送，继续等待
                self.root.after(1000, self._wait_for_response_and_continue)
                return

            # 检查游戏是否结束
            if self._check_if_game_ended():
                print("[自动测试] 检测到游戏结束，停止自动测试")
                self._log_auto_test("检测到游戏结束，停止自动测试")
                self._stop_auto_test()
                return

            # 继续下一轮
            self.auto_execute_timer_id = self.root.after(1000, self._execute_auto_round)

        except Exception as e:
            print(f"[自动测试] 等待响应时出错: {e}")
            self._log_auto_test(f"等待响应时出错: {e}")

    def _check_if_game_ended(self):
        """检查游戏是否结束"""
        try:
            # 检查消息文本中是否包含游戏结束标记
            message_content = self.message_text.get(1.0, tk.END)
            game_over_markers = [
                '{gameover}',
                '{GAMEOVER}',
                '{GameOver}',
                '{game_over}',
                '{GAME_OVER}',
                '游戏结束',
                '游戏已结束'
            ]

            for marker in game_over_markers:
                if marker in message_content:
                    return True

            return False

        except Exception as e:
            print(f"[自动测试] 检查游戏结束状态时出错: {e}")
            return False

    def _init_auto_test_log(self):
        """初始化自动测试日志文件"""
        try:
            # 创建日志目录
            log_dir = Path("logs/auto_test")
            log_dir.mkdir(parents=True, exist_ok=True)

            # 生成日志文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.auto_test_log_file = log_dir / f"auto_test_{timestamp}.log"

            # 写入日志头
            with open(self.auto_test_log_file, 'w', encoding='utf-8') as f:
                f.write(f"自动测试日志 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")

            print(f"[自动测试] 日志文件已创建: {self.auto_test_log_file}")

        except Exception as e:
            print(f"[自动测试] 初始化日志文件失败: {e}")
            self.auto_test_log_file = None

    def _log_auto_test(self, message, level="INFO"):
        """记录自动测试日志"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_message = f"[自动测试 {timestamp}] [{level}] {message}"

            # 在消息区域显示日志（只显示重要信息）
            if level in ["INFO", "ERROR", "WARNING"]:
                self.message_text.config(state=tk.NORMAL)
                self.message_text.insert(tk.END, f"{log_message}\n", "system")
                self.message_text.config(state=tk.DISABLED)
                self.message_text.see(tk.END)

            # 输出到控制台
            print(log_message)

            # 写入日志文件
            if self.auto_test_log_file:
                try:
                    with open(self.auto_test_log_file, 'a', encoding='utf-8') as f:
                        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} [{level}] {message}\n")
                except Exception as e:
                    print(f"[自动测试] 写入日志文件失败: {e}")

        except Exception as e:
            print(f"[自动测试] 记录日志时出错: {e}")

    def _log_auto_test_summary(self):
        """记录自动测试总结"""
        try:
            if not self.auto_test_start_time:
                return

            end_time = datetime.now()
            duration = end_time - self.auto_test_start_time

            summary = f"""
自动测试完成总结:
- 开始时间: {self.auto_test_start_time.strftime('%Y-%m-%d %H:%M:%S')}
- 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
- 持续时间: {duration}
- 完成回合数: {self.current_auto_round}
- 目标回合数: {self.auto_execute_rounds.get()}
- 完成率: {(self.current_auto_round / self.auto_execute_rounds.get() * 100):.1f}%
"""

            self._log_auto_test(summary, "SUMMARY")

            if self.auto_test_log_file:
                with open(self.auto_test_log_file, 'a', encoding='utf-8') as f:
                    f.write("\n" + "=" * 50 + "\n")
                    f.write(summary)
                    f.write("\n" + "=" * 50 + "\n")

        except Exception as e:
            print(f"[自动测试] 记录测试总结时出错: {e}")


def create_and_run_gui():
    """创建并运行GUI应用"""
    try:
        app = ChatGUI()
        app.run()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        messagebox.showerror("启动错误", f"启动GUI失败: {str(e)}")


if __name__ == "__main__":
    create_and_run_gui()

