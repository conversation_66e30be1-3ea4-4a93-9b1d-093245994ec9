#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎（豆包）大模型API简化测试脚本
专门用于验证API密钥和连接性
"""

import os
import sys
import json
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_environment():
    """加载环境变量"""
    try:
        from dotenv import load_dotenv
        env_path = project_root / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            print(f"✓ 已加载环境变量文件: {env_path}")
        else:
            print(f"⚠ 环境变量文件不存在: {env_path}")
            return False
        return True
    except ImportError:
        print("⚠ python-dotenv 未安装，尝试直接读取系统环境变量")
        return True

def get_api_key():
    """获取API密钥"""
    api_key = os.getenv('volcengine_API_KEY')
    if not api_key:
        print("✗ 未找到火山引擎API密钥")
        print("请在.env文件中设置: volcengine_API_KEY='your_api_key_here'")
        return None
    
    if api_key.strip() == '':
        print("✗ 火山引擎API密钥为空")
        print("请在.env文件中设置有效的API密钥")
        return None
    
    print(f"✓ 已获取API密钥: {api_key[:10]}...")
    return api_key

def test_api_endpoint(api_key: str):
    """测试API端点连接性"""
    print("\n" + "=" * 60)
    print("测试火山引擎API端点连接性...")
    print("=" * 60)
    
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 使用一个简单的测试请求
    test_data = {
        "messages": [
            {"role": "user", "content": "hello"}
        ],
        "model": "test-model",  # 故意使用不存在的模型来测试认证
        "max_tokens": 10
    }
    
    try:
        print(f"✓ 发送测试请求到: {url}")
        response = requests.post(url, headers=headers, json=test_data, timeout=10)
        
        print(f"✓ 收到响应，状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("✗ API密钥认证失败")
            print("  请检查API密钥是否正确")
            return False
        elif response.status_code == 404:
            try:
                error_info = response.json()
                error_message = error_info.get('error', {}).get('message', '')
                
                if 'model' in error_message.lower() or 'endpoint' in error_message.lower():
                    print("✓ API密钥认证成功！")
                    print("  (收到模型不存在错误，说明认证通过)")
                    return True
                else:
                    print(f"✗ 未知错误: {error_message}")
                    return False
            except:
                print("✗ 响应格式异常")
                return False
        elif response.status_code == 200:
            print("✓ API连接完全正常！")
            return True
        else:
            print(f"✗ 未预期的状态码: {response.status_code}")
            try:
                error_info = response.json()
                print(f"  错误信息: {error_info}")
            except:
                print(f"  响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("✗ 请求超时")
        print("  建议: 检查网络连接")
        return False
    except requests.exceptions.ConnectionError:
        print("✗ 连接错误")
        print("  建议: 检查网络连接和防火墙设置")
        return False
    except Exception as e:
        print(f"✗ 发生未预期错误: {e}")
        return False

def show_setup_guide():
    """显示设置指南"""
    print("\n" + "=" * 60)
    print("火山引擎方舟平台设置指南")
    print("=" * 60)
    
    print("\n🔑 API密钥获取:")
    print("1. 访问火山引擎控制台: https://console.volcengine.com/")
    print("2. 进入「方舟」产品")
    print("3. 在左侧菜单选择「API管理」")
    print("4. 创建或查看API密钥")
    print("5. 将密钥配置到.env文件中")
    
    print("\n🚀 模型激活:")
    print("1. 在方舟控制台进入「模型广场」")
    print("2. 选择需要的模型（推荐模型）:")
    print("   - doubao-pro-4k: 适合日常对话，4K上下文")
    print("   - doubao-pro-32k: 适合长文本，32K上下文")
    print("   - doubao-1-5-pro-32k-250115: 最新版本，性能更好")
    print("3. 点击「开通」按钮激活模型")
    print("4. 等待几分钟完成激活")
    
    print("\n📚 API使用:")
    print("- 端点: https://ark.cn-beijing.volces.com/api/v3/chat/completions")
    print("- 兼容OpenAI API格式")
    print("- 支持流式和非流式响应")
    print("- 请求头需要包含: Authorization: Bearer YOUR_API_KEY")
    
    print("\n💡 常见问题:")
    print("- 如果遇到模型未激活错误，请先在控制台激活对应模型")
    print("- API密钥有使用限制，请注意配额管理")
    print("- 建议在生产环境中使用环境变量管理API密钥")

def main():
    """主函数"""
    print("=" * 60)
    print("火山引擎（豆包）大模型API连接测试")
    print("=" * 60)
    
    # 1. 加载环境变量
    if not load_environment():
        return False
    
    # 2. 获取API密钥
    api_key = get_api_key()
    if not api_key:
        show_setup_guide()
        return False
    
    # 3. 测试API连接
    success = test_api_endpoint(api_key)
    
    # 4. 显示设置指南
    show_setup_guide()
    
    if success:
        print("\n" + "=" * 60)
        print("✓ 火山引擎API连接测试成功！")
        print("  您的API密钥有效，可以开始使用火山引擎大模型服务")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("✗ 火山引擎API连接测试失败")
        print("  请参考上述设置指南检查配置")
        print("=" * 60)
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
