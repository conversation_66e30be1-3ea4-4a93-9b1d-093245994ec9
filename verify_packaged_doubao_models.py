#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证打包后的程序是否包含豆包模型
可以在dist目录下运行此脚本进行验证
"""

import os
import sys
from pathlib import Path


def check_packaged_environment():
    """检查打包后的环境"""
    print("🔍 检查打包后的环境")
    print("=" * 60)
    
    current_dir = Path.cwd()
    print(f"当前目录: {current_dir}")
    
    # 检查关键文件
    key_files = [
        ".env",
        "iNiverse_Chat_Client_With_Doubao.exe",
        "_internal",
    ]
    
    print("\n1. 检查关键文件...")
    for file_name in key_files:
        file_path = current_dir / file_name
        if file_path.exists():
            print(f"  ✅ {file_name}")
        else:
            print(f"  ❌ {file_name}")
    
    # 检查配置目录
    print("\n2. 检查配置目录...")
    config_paths = [
        "_internal/deepseek_chat_client/config",
        "deepseek_chat_client/config",
    ]
    
    config_found = False
    for config_path in config_paths:
        full_path = current_dir / config_path
        if full_path.exists():
            config_files = list(full_path.glob("*.yaml"))
            print(f"  ✅ {config_path}: {len(config_files)}个配置文件")
            for config_file in config_files:
                print(f"    - {config_file.name}")
            config_found = True
            break
    
    if not config_found:
        print("  ❌ 未找到配置目录")
    
    # 检查环境变量文件
    print("\n3. 检查环境变量文件...")
    env_file = current_dir / ".env"
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"  ✅ .env文件存在，包含{len(lines)}行")
            
            # 检查关键环境变量（不显示实际值）
            env_vars = {}
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = len(value.strip()) > 0
            
            key_vars = ['DEEPSEEK_API_KEY', 'VOLCENGINE_API_KEY']
            for var in key_vars:
                if var in env_vars:
                    status = "已配置" if env_vars[var] else "空值"
                    print(f"    {var}: {status}")
                else:
                    print(f"    {var}: 未找到")
                    
        except Exception as e:
            print(f"  ❌ 读取.env文件失败: {e}")
    else:
        print("  ❌ .env文件不存在")


def simulate_model_loading():
    """模拟模型加载过程"""
    print("\n🧪 模拟模型加载过程")
    print("=" * 60)
    
    try:
        # 设置Python路径
        current_dir = Path.cwd()
        internal_dir = current_dir / "_internal"
        
        if internal_dir.exists():
            sys.path.insert(0, str(internal_dir))
            print(f"✅ 添加_internal目录到Python路径")
        
        # 尝试导入模块
        print("\n1. 尝试导入核心模块...")
        try:
            import deepseek_chat_client
            print("  ✅ deepseek_chat_client")
        except ImportError as e:
            print(f"  ❌ deepseek_chat_client: {e}")
            return False
        
        try:
            from deepseek_chat_client.volcengine_client import VolcengineAPIClient
            print("  ✅ VolcengineAPIClient")
        except ImportError as e:
            print(f"  ❌ VolcengineAPIClient: {e}")
            return False
        
        try:
            from deepseek_chat_client.unified_client import UnifiedAPIClient
            print("  ✅ UnifiedAPIClient")
        except ImportError as e:
            print(f"  ❌ UnifiedAPIClient: {e}")
            return False
        
        # 测试配置加载
        print("\n2. 测试配置加载...")
        try:
            from deepseek_chat_client.config import config
            print("  ✅ 配置模块导入成功")
            
            # 测试API密钥
            deepseek_key = config.get_api_key()
            volcengine_key = config.get_app_setting("volcengine.api_key")
            
            print(f"  DeepSeek API密钥: {'已配置' if deepseek_key else '未配置'}")
            print(f"  火山引擎API密钥: {'已配置' if volcengine_key else '未配置'}")
            
        except Exception as e:
            print(f"  ❌ 配置加载失败: {e}")
            return False
        
        # 测试模型列表
        print("\n3. 测试模型列表...")
        try:
            volcengine_client = VolcengineAPIClient()
            print(f"  火山引擎客户端创建成功")
            print(f"  API可用性: {volcengine_client.is_available()}")
            
            models = volcengine_client.get_available_models()
            print(f"  豆包模型数量: {len(models)}")
            
            if models:
                print("  豆包模型列表:")
                for model_id, model_name in models.items():
                    print(f"    - {model_id}: {model_name}")
            else:
                print("  ❌ 未找到豆包模型")
                return False
            
        except Exception as e:
            print(f"  ❌ 模型列表测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试统一客户端
        print("\n4. 测试统一客户端...")
        try:
            unified_client = UnifiedAPIClient()
            all_models = unified_client.get_available_models()
            
            doubao_models = []
            for model_id, model_name in all_models.items():
                provider = unified_client.get_provider_for_model(model_id)
                if provider == "volcengine":
                    doubao_models.append((model_id, model_name))
            
            print(f"  统一客户端中的豆包模型数量: {len(doubao_models)}")
            
            if doubao_models:
                print("  豆包模型列表:")
                for model_id, model_name in doubao_models:
                    print(f"    - {model_id}: {model_name}")
                return True
            else:
                print("  ❌ 统一客户端中未找到豆包模型")
                return False
                
        except Exception as e:
            print(f"  ❌ 统一客户端测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 模型加载模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def provide_troubleshooting_tips():
    """提供故障排除建议"""
    print("\n💡 故障排除建议")
    print("=" * 60)
    
    print("如果豆包模型仍然不显示，请检查以下问题：")
    print()
    
    print("1. 🔑 API密钥问题")
    print("   - 确保.env文件存在且包含VOLCENGINE_API_KEY")
    print("   - 检查API密钥是否正确且有效")
    print("   - 确认API密钥有足够的额度")
    print()
    
    print("2. 📁 文件包含问题")
    print("   - 确保.env文件被正确复制到可执行文件目录")
    print("   - 检查deepseek_chat_client/config/目录是否存在")
    print("   - 验证所有YAML配置文件是否包含")
    print()
    
    print("3. 🌐 网络连接问题")
    print("   - 确认网络连接正常")
    print("   - 检查防火墙设置")
    print("   - 尝试使用VPN或更换网络环境")
    print()
    
    print("4. 🔧 重新打包建议")
    print("   - 使用提供的build_with_doubao_models.py脚本重新打包")
    print("   - 确保使用最新的spec文件配置")
    print("   - 检查PyInstaller版本是否兼容")
    print()
    
    print("5. 🐛 调试方法")
    print("   - 在可执行文件目录下运行此验证脚本")
    print("   - 查看控制台输出的错误信息")
    print("   - 检查是否有模块导入失败的错误")


def main():
    """主函数"""
    print("🔍 验证打包后的豆包模型支持")
    print("=" * 80)
    
    print("此脚本应在打包后的可执行文件目录下运行")
    print("例如：dist/iNiverse_Chat_Client_With_Doubao/")
    print()
    
    # 检查环境
    check_packaged_environment()
    
    # 模拟模型加载
    success = simulate_model_loading()
    
    # 提供故障排除建议
    provide_troubleshooting_tips()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 验证成功！豆包模型应该可以正常显示")
        print("\n✅ 建议：")
        print("- 运行主程序，检查模型选择列表")
        print("- 尝试选择豆包模型进行对话")
        print("- 如果仍有问题，请检查网络连接和API密钥")
    else:
        print("❌ 验证失败！豆包模型可能无法正常显示")
        print("\n🔧 建议：")
        print("- 检查上述错误信息")
        print("- 使用build_with_doubao_models.py重新打包")
        print("- 确保所有必要文件都被正确包含")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
