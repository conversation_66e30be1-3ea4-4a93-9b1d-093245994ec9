#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境变量加载测试工具
用于测试开发环境和打包环境下的.env文件读取
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_environment_detection():
    """测试环境检测"""
    print("=== 环境检测 ===")
    
    print(f"Python版本: {sys.version}")
    print(f"运行环境: {'打包环境' if getattr(sys, 'frozen', False) else '开发环境'}")
    print(f"可执行文件路径: {sys.executable}")
    print(f"脚本文件路径: {__file__}")
    print(f"当前工作目录: {Path.cwd()}")
    
    if getattr(sys, 'frozen', False):
        print(f"打包环境详情:")
        if hasattr(sys, '_MEIPASS'):
            print(f"  PyInstaller临时目录: {sys._MEIPASS}")
        print(f"  可执行文件目录: {Path(sys.executable).parent}")
    
    print()


def test_path_resolution():
    """测试路径解析"""
    print("=== 路径解析测试 ===")
    
    # 测试不同的路径计算方法
    methods = {
        "Path(__file__).parent": Path(__file__).parent,
        "Path.cwd()": Path.cwd(),
        "os.getcwd()": Path(os.getcwd()),
    }
    
    if getattr(sys, 'frozen', False):
        methods["Path(sys.executable).parent"] = Path(sys.executable).parent
        if hasattr(sys, '_MEIPASS'):
            methods["Path(sys._MEIPASS)"] = Path(sys._MEIPASS)
    
    for method_name, path in methods.items():
        print(f"{method_name}: {path}")
        env_file = path / ".env"
        print(f"  .env存在: {env_file.exists()}")
        if env_file.exists():
            print(f"  .env路径: {env_file}")
    
    print()


def test_env_file_search():
    """测试.env文件搜索"""
    print("=== .env文件搜索测试 ===")
    
    # 可能的.env文件位置
    search_paths = [
        Path(__file__).parent / ".env",
        Path.cwd() / ".env",
        Path(os.getcwd()) / ".env",
    ]
    
    if getattr(sys, 'frozen', False):
        search_paths.extend([
            Path(sys.executable).parent / ".env",
        ])
        if hasattr(sys, '_MEIPASS'):
            search_paths.append(Path(sys._MEIPASS) / ".env")
    
    print("搜索路径:")
    found_files = []
    for i, path in enumerate(search_paths, 1):
        exists = path.exists()
        print(f"  {i}. {path} - {'✓' if exists else '✗'}")
        if exists:
            found_files.append(path)
    
    if found_files:
        print(f"\n找到 {len(found_files)} 个.env文件:")
        for env_file in found_files:
            print(f"  {env_file}")
            try:
                with open(env_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    lines = content.split('\n')
                    print(f"    行数: {len(lines)}")
                    for line in lines[:3]:  # 只显示前3行
                        if 'KEY' in line:
                            key, value = line.split('=', 1)
                            print(f"    {key}={value[:10]}...")
                        else:
                            print(f"    {line}")
            except Exception as e:
                print(f"    读取失败: {e}")
    else:
        print("\n未找到.env文件")
    
    print()


def test_env_manager():
    """测试环境变量管理器"""
    print("=== 环境变量管理器测试 ===")
    
    try:
        from deepseek_chat_client.env_manager import EnvironmentManager
        
        env_mgr = EnvironmentManager()
        
        # 测试API密钥获取
        try:
            api_key = env_mgr.get_required('DEEPSEEK_API_KEY')
            print(f"✓ API密钥获取成功: {api_key[:10]}...{api_key[-4:]}")
        except ValueError as e:
            print(f"✗ API密钥获取失败: {e}")
        
        # 显示敏感环境变量
        sensitive_vars = env_mgr.list_sensitive()
        key_vars = {k: v for k, v in sensitive_vars.items() if 'KEY' in k or 'TOKEN' in k}
        
        if key_vars:
            print(f"敏感环境变量 ({len(key_vars)}个):")
            for key, value in key_vars.items():
                print(f"  {key}: {value}")
        else:
            print("未找到敏感环境变量")
            
    except ImportError as e:
        print(f"✗ 无法导入环境变量管理器: {e}")
    except Exception as e:
        print(f"✗ 环境变量管理器测试失败: {e}")
    
    print()


def test_config_loading():
    """测试配置加载"""
    print("=== 配置加载测试 ===")
    
    try:
        from deepseek_chat_client.config import Config
        
        config = Config()
        
        print(f"✓ 配置加载成功")
        print(f"项目根目录: {config.project_root}")
        print(f"配置目录: {config.config_dir}")
        
        if config.api_key:
            print(f"✓ API密钥: {config.api_key[:10]}...{config.api_key[-4:]}")
        else:
            print("✗ API密钥未加载")
        
        print(f"系统提示词数量: {len(config.system_prompts)}")
        print(f"应用设置数量: {len(config.app_settings)}")
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()


def test_system_env_vars():
    """测试系统环境变量"""
    print("=== 系统环境变量测试 ===")
    
    # 检查相关的系统环境变量
    relevant_vars = [
        'DEEPSEEK_API_KEY',
        'PATH',
        'PYTHONPATH',
        'HOME',
        'USERPROFILE',
        'APPDATA',
        'LOCALAPPDATA',
    ]
    
    for var in relevant_vars:
        value = os.environ.get(var)
        if value:
            if 'KEY' in var or 'TOKEN' in var:
                print(f"  {var}: {value[:10]}...{value[-4:]}")
            elif len(value) > 100:
                print(f"  {var}: {value[:50]}...{value[-20:]}")
            else:
                print(f"  {var}: {value}")
        else:
            print(f"  {var}: (未设置)")
    
    print()


def main():
    """主测试函数"""
    print("iNiverse Chat Client - 环境变量加载测试")
    print("=" * 60)
    
    test_environment_detection()
    test_path_resolution()
    test_env_file_search()
    test_system_env_vars()
    test_env_manager()
    test_config_loading()
    
    print("=" * 60)
    print("测试完成")
    
    # 等待用户输入（在打包环境下防止窗口立即关闭）
    if getattr(sys, 'frozen', False):
        input("\n按回车键退出...")


if __name__ == "__main__":
    main()
