#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI集成测试
测试Function Calling在GUI中的集成情况
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui_function_calling():
    """测试GUI中的Function Calling功能"""
    print("=" * 60)
    print("测试GUI Function Calling集成")
    print("=" * 60)
    
    try:
        # 导入GUI相关模块
        from deepseek_chat_client.gui import ChatGUI
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        
        print("✓ GUI模块导入成功")
        
        # 测试统一客户端的Function Calling功能
        client = UnifiedAPIClient()
        print(f"✓ Function Calling启用状态: {client.is_function_calling_enabled()}")
        
        # 测试获取可用函数
        functions = client.get_available_functions()
        print(f"✓ 可用函数数量: {len(functions) if functions else 0}")
        
        if functions:
            print("✓ 可用函数列表:")
            for func in functions[:5]:  # 只显示前5个
                print(f"  - {func['function']['name']}: {func['function']['description']}")
        
        # 测试Function Calling统计
        stats = client.get_function_calling_stats()
        print(f"✓ 注册的函数总数: {stats['registry']['total_functions']}")
        print(f"✓ 启用的函数数量: {stats['registry']['enabled_functions']}")
        print(f"✓ 函数分类数量: {stats['registry']['categories']}")
        
        print("✓ GUI Function Calling集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GUI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dialogue_window_integration():
    """测试对话窗口的Function Calling集成"""
    print("\n" + "=" * 60)
    print("测试对话窗口Function Calling集成")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.dialogue_window import DialogueWindow
        from deepseek_chat_client.gui import ChatGUI
        
        print("✓ 对话窗口模块导入成功")
        
        # 模拟NPC数据
        npc_data = [
            "测试NPC",  # NPC名字
            "测试身份",  # NPC身份信息
            "你是一个测试NPC",  # 系统提示词
            "测试背景",  # NPC背景
            "友好的性格",  # NPC性格
            "当前测试情况",  # 对话上下文
            "当前关键聊天剧情设计限制(随机填入5-10)回合数:\"3回合\""  # 回合限制
        ]
        
        print("✓ 对话窗口Function Calling集成测试通过（模拟）")
        return True
        
    except Exception as e:
        print(f"✗ 对话窗口集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_function_calling_config():
    """测试Function Calling配置"""
    print("\n" + "=" * 60)
    print("测试Function Calling配置")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.function_calling import function_calling_config
        
        # 测试配置读取
        print(f"✓ 启用状态: {function_calling_config.is_enabled()}")
        print(f"✓ 超时时间: {function_calling_config.get_timeout()}秒")
        print(f"✓ 最大并发数: {function_calling_config.get_max_concurrent_calls()}")
        print(f"✓ 内置函数启用: {function_calling_config.is_builtin_functions_enabled()}")
        print(f"✓ 日志级别: {function_calling_config.get_log_level()}")
        print(f"✓ 日志文件: {function_calling_config.get_log_file()}")
        print(f"✓ 调试模式: {function_calling_config.is_debug_enabled()}")
        
        # 测试允许的分类
        categories = function_calling_config.get_allowed_categories()
        print(f"✓ 允许的函数分类: {categories}")
        
        # 测试禁用的函数
        disabled = function_calling_config.get_disabled_functions()
        print(f"✓ 禁用的函数: {disabled}")
        
        print("✓ Function Calling配置测试通过")
        return True
        
    except Exception as e:
        print(f"✗ Function Calling配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_logging_functionality():
    """测试日志功能"""
    print("\n" + "=" * 60)
    print("测试日志功能")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.function_calling import FunctionExecutor, function_calling_config
        from deepseek_chat_client.function_calling.executor import FunctionCallRequest
        
        # 创建执行器（会初始化日志）
        executor = FunctionExecutor()
        
        # 执行一个函数调用来生成日志
        request = FunctionCallRequest(
            name="get_current_time",
            arguments={},
            call_id="log_test_001"
        )
        
        result = executor.execute_function_call(request)
        print(f"✓ 函数调用结果: {result.success}")
        
        # 检查日志文件是否存在
        log_file = function_calling_config.get_log_file()
        if os.path.exists(log_file):
            print(f"✓ 日志文件存在: {log_file}")
            
            # 读取最后几行日志
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print("✓ 最近的日志条目:")
                    for line in lines[-3:]:  # 显示最后3行
                        print(f"  {line.strip()}")
        else:
            print(f"⚠ 日志文件不存在: {log_file}")
        
        print("✓ 日志功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 日志功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试错误处理")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.function_calling import FunctionExecutor
        from deepseek_chat_client.function_calling.executor import FunctionCallRequest
        
        executor = FunctionExecutor(timeout=1.0)  # 短超时时间
        
        # 测试不存在的函数
        request1 = FunctionCallRequest(
            name="non_existent_function",
            arguments={},
            call_id="error_test_001"
        )
        
        result1 = executor.execute_function_call(request1)
        print(f"✓ 不存在函数处理: success={result1.success}, error='{result1.error}'")
        
        # 测试参数错误
        request2 = FunctionCallRequest(
            name="calculate",
            arguments={"expression": "invalid_expression"},
            call_id="error_test_002"
        )
        
        result2 = executor.execute_function_call(request2)
        print(f"✓ 参数错误处理: success={result2.success}")
        
        print("✓ 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有GUI集成测试"""
    print("开始GUI Function Calling集成测试")
    print("=" * 80)
    
    tests = [
        test_gui_function_calling,
        test_dialogue_window_integration,
        test_function_calling_config,
        test_logging_functionality,
        test_error_handling
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 异常: {e}")
            failed += 1
    
    print("\n" + "=" * 80)
    print(f"GUI集成测试完成: {passed} 通过, {failed} 失败")
    print("=" * 80)
    
    if failed == 0:
        print("🎉 所有Function Calling功能测试通过！")
        print("系统已准备就绪，可以开始使用Function Calling功能。")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
