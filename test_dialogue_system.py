#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
触发关键聊天剧情系统测试脚本
测试对话窗口的创建、触发检测和完整功能
"""

import sys
import json
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI
from deepseek_chat_client.dialogue_window import DialogueWindow


def test_trigger_detection():
    """测试触发检测功能"""
    print("=" * 50)
    print("测试触发检测功能")
    print("=" * 50)
    
    # 创建GUI实例（但不显示）
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏GUI窗口
        
        # 测试用例1：标准触发格式
        print("\n1. 测试标准触发格式...")
        test_content_1 = '''
        游戏剧情内容...
        
        {"触发关键聊天剧情": ["苏瑶", "正道/太清宗/外门弟子 炼气九层", "你是苏瑶，太清宗的外门弟子", "你与玩家是同门师兄弟关系", "你性格温和，乐于助人", "当前在宗门广场遇到了玩家", "当前关键聊天剧情设计限制(随机填入5-10)回合数:6回合"]}
        
        更多游戏内容...
        '''
        
        result_1 = gui._check_dialogue_trigger(test_content_1)
        if result_1:
            print("✓ 标准格式触发检测成功")
        else:
            print("✗ 标准格式触发检测失败")
        
        # 测试用例2：包含其他字段的格式
        print("\n2. 测试包含其他字段的格式...")
        test_content_2 = '''
        {"quick_replies": ["选项1", "选项2"], "触发关键聊天剧情": ["张三", "散修/筑基初期", "你是张三", "背景信息", "性格描述", "对话情境", "5回合"], "other_field": "value"}
        '''
        
        result_2 = gui._check_dialogue_trigger(test_content_2)
        if result_2:
            print("✓ 复合格式触发检测成功")
        else:
            print("✗ 复合格式触发检测失败")
        
        # 测试用例3：错误格式（元素数量不对）
        print("\n3. 测试错误格式（元素数量不对）...")
        test_content_3 = '''
        {"触发关键聊天剧情": ["苏瑶", "太清宗弟子", "系统提示词"]}
        '''
        
        result_3 = gui._check_dialogue_trigger(test_content_3)
        if not result_3:
            print("✓ 错误格式正确拒绝")
        else:
            print("✗ 错误格式应该被拒绝但被接受了")
        
        # 测试用例4：无触发内容
        print("\n4. 测试无触发内容...")
        test_content_4 = '''
        普通的游戏内容，没有触发标记
        {"quick_replies": ["选项1", "选项2"]}
        '''
        
        result_4 = gui._check_dialogue_trigger(test_content_4)
        if not result_4:
            print("✓ 无触发内容正确处理")
        else:
            print("✗ 无触发内容错误触发")
        
        print("\n触发检测测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 触发检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def test_dialogue_window_creation():
    """测试对话窗口创建"""
    print("=" * 50)
    print("测试对话窗口创建")
    print("=" * 50)
    
    # 创建GUI实例
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        # 测试NPC数据
        npc_data = [
            "苏瑶",  # NPC名字
            "正道/太清宗/外门弟子 炼气九层",  # NPC身份
            "你是苏瑶，太清宗的外门弟子，修为炼气九层。",  # 系统提示词
            "你与玩家是同门师兄弟关系，平时关系不错。",  # 背景关系
            "你性格温和，乐于助人，对修炼很有热情。",  # 性格动机
            "当前在宗门广场遇到了玩家，你刚刚完成了一次修炼。",  # 对话情境
            "当前关键聊天剧情设计限制(随机填入5-10)回合数:6回合"  # 回合限制
        ]
        
        print("1. 测试对话窗口创建...")
        
        def mock_dialogue_complete(history):
            print(f"对话完成回调被调用，历史长度: {len(history)}")
        
        # 创建对话窗口
        dialogue_window = DialogueWindow(
            parent_gui=gui,
            npc_data=npc_data,
            on_dialogue_complete=mock_dialogue_complete
        )
        
        print("✓ 对话窗口创建成功")
        
        # 测试数据解析
        print("2. 测试数据解析...")
        assert dialogue_window.npc_name == "苏瑶", f"NPC名字解析错误: {dialogue_window.npc_name}"
        assert dialogue_window.npc_identity == "正道/太清宗/外门弟子 炼气九层", f"NPC身份解析错误: {dialogue_window.npc_identity}"
        assert dialogue_window.turn_limit == 6, f"回合限制解析错误: {dialogue_window.turn_limit}"
        print("✓ 数据解析正确")
        
        # 测试回合限制解析
        print("3. 测试回合限制解析...")
        test_cases = [
            ("5回合", 5),
            ("10回合", 10),
            ("当前关键聊天剧情设计限制(随机填入5-10)回合数:8回合", 8),
            ("无效格式", 6)  # 默认值
        ]
        
        for test_input, expected in test_cases:
            result = dialogue_window._extract_turn_limit(test_input)
            if result == expected:
                print(f"  ✓ '{test_input}' -> {result}")
            else:
                print(f"  ✗ '{test_input}' -> {result}, 期望 {expected}")
        
        print("✓ 回合限制解析测试完成")
        
        # 关闭对话窗口
        dialogue_window.window.destroy()
        
        print("\n对话窗口创建测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 对话窗口创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def test_json_parsing():
    """测试JSON解析功能"""
    print("=" * 50)
    print("测试JSON解析功能")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "标准格式",
            "content": '{"触发关键聊天剧情": ["苏瑶", "太清宗弟子", "系统提示", "背景", "性格", "情境", "6回合"]}',
            "should_trigger": True
        },
        {
            "name": "包含换行的格式",
            "content": '''
            {
                "触发关键聊天剧情": [
                    "苏瑶",
                    "太清宗弟子",
                    "系统提示",
                    "背景",
                    "性格",
                    "情境",
                    "6回合"
                ]
            }
            ''',
            "should_trigger": True
        },
        {
            "name": "混合其他字段",
            "content": '{"other": "value", "触发关键聊天剧情": ["苏瑶", "太清宗弟子", "系统提示", "背景", "性格", "情境", "6回合"], "more": "data"}',
            "should_trigger": True
        },
        {
            "name": "元素数量不足",
            "content": '{"触发关键聊天剧情": ["苏瑶", "太清宗弟子"]}',
            "should_trigger": False
        },
        {
            "name": "无触发字段",
            "content": '{"quick_replies": ["选项1", "选项2"]}',
            "should_trigger": False
        }
    ]
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        success_count = 0
        total_count = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试 {test_case['name']}...")
            result = gui._check_dialogue_trigger(test_case['content'])
            
            if result == test_case['should_trigger']:
                print(f"  ✓ 结果正确: {result}")
                success_count += 1
            else:
                print(f"  ✗ 结果错误: 期望 {test_case['should_trigger']}, 实际 {result}")
        
        print(f"\nJSON解析测试完成: {success_count}/{total_count} 通过")
        return success_count == total_count
        
    except Exception as e:
        print(f"✗ JSON解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def main():
    """主测试函数"""
    print("开始触发关键聊天剧情系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("触发检测功能", test_trigger_detection()))
    test_results.append(("对话窗口创建", test_dialogue_window_creation()))
    test_results.append(("JSON解析功能", test_json_parsing()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！触发关键聊天剧情系统实现成功！")
        return 0
    else:
        print("❌ 部分测试失败，需要修复问题")
        return 1


if __name__ == "__main__":
    sys.exit(main())
