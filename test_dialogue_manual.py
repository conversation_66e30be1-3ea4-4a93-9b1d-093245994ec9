#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
触发关键聊天剧情系统手动测试
用于验证GUI集成和实际使用场景
"""

import sys
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI


def create_test_gui():
    """创建测试GUI"""
    print("创建测试GUI...")
    
    # 创建主窗口
    app = ChatGUI()
    
    # 模拟AI响应，包含触发关键聊天剧情的内容
    test_response = '''
〖第一章1/100〗第1回，〖青云山门〗青云山坐落在云雾缭绕的山峰之上，山门巍峨壮观。

【张三】修为筑基初期，正在山门前犹豫不决。

「内心活动」这里就是传说中的修仙门派吗？我真的能在这里学到仙法吗？

{"触发关键聊天剧情": ["苏瑶", "正道/太清宗/外门弟子 炼气九层", "你是苏瑶，太清宗的外门弟子，修为炼气九层。你对修炼很有心得，经常帮助同门师兄弟解答疑问。", "你与玩家是同门师兄弟关系，平时关系不错，经常一起讨论修炼心得。", "你性格温和，乐于助人，对修炼很有热情。你总是耐心地回答别人的问题，从不嫌麻烦。", "当前在宗门广场遇到了玩家，你刚刚完成了一次修炼，正准备回房间休息。看到玩家似乎有些困惑的样子。", "当前关键聊天剧情设计限制(随机填入5-10)回合数:6回合"]}

【系统提示】你可以选择与苏瑶进行深入对话，了解更多修炼心得。

```json
{"quick_replies":["走向山门", "观察周围", "寻找其他弟子", "暂时离开"]}
```
    '''
    
    # 添加测试按钮
    def trigger_test():
        """触发测试"""
        print("手动触发测试响应...")
        
        # 模拟接收到AI响应
        app._display_message("assistant", test_response)
        app.history_manager.add_message("assistant", test_response)
        
        # 解析并显示（这会触发对话窗口）
        app._parse_and_show_quick_replies(test_response)
    
    # 在GUI中添加测试按钮
    test_frame = tk.Frame(app.main_frame)
    test_frame.pack(fill=tk.X, pady=5)
    
    test_button = tk.Button(
        test_frame,
        text="触发测试对话",
        command=trigger_test,
        bg="#ff6b6b",
        fg="white",
        font=("Microsoft YaHei", 10, "bold")
    )
    test_button.pack(side=tk.LEFT, padx=5)
    
    # 添加说明标签
    info_label = tk.Label(
        test_frame,
        text="点击按钮测试触发关键聊天剧情功能",
        font=("Microsoft YaHei", 9),
        fg="#666666"
    )
    info_label.pack(side=tk.LEFT, padx=10)
    
    print("测试GUI创建完成")
    print("=" * 50)
    print("使用说明：")
    print("1. 点击'触发测试对话'按钮")
    print("2. 应该会弹出对话窗口，显示与苏瑶的对话")
    print("3. 主窗口输入应该被锁定")
    print("4. 在对话窗口中可以与NPC进行对话")
    print("5. 达到6回合限制后对话自动结束")
    print("6. 对话历史会被添加到主聊天记录中")
    print("=" * 50)
    
    return app


def main():
    """主函数"""
    print("启动触发关键聊天剧情系统手动测试")
    print("=" * 60)
    
    try:
        # 创建测试GUI
        app = create_test_gui()
        
        # 运行GUI
        app.run()
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return 0
    except Exception as e:
        print(f"测试运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("测试完成")
    return 0


if __name__ == "__main__":
    sys.exit(main())
