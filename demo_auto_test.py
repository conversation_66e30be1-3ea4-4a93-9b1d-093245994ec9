#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动测试功能演示脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """启动带有自动测试功能的GUI"""
    try:
        print("=" * 60)
        print("🎮 iNiverse 文本游戏 - 自动测试功能演示")
        print("=" * 60)
        print()
        print("功能特性：")
        print("✓ 自动随机选择游戏选项")
        print("✓ 自动执行多个回合")
        print("✓ 可设定回合数量（1-1000）")
        print("✓ 详细日志记录")
        print("✓ 实时状态监控")
        print()
        print("使用方法：")
        print("1. 点击'新建会话'创建游戏会话")
        print("2. 点击'开始游戏'启动游戏")
        print("3. 在右侧'自动测试'面板中：")
        print("   - 勾选'自动选择'和'自动执行'")
        print("   - 设定回合数量")
        print("   - 点击'开始自动测试'")
        print("4. 观察自动测试过程和日志")
        print()
        print("注意事项：")
        print("- 确保选择了合适的系统提示词文件")
        print("- 建议先用较少回合数测试")
        print("- 可随时点击'停止自动测试'中断")
        print("- 日志文件保存在 logs/auto_test/ 目录")
        print()
        print("正在启动GUI...")
        print("=" * 60)
        
        from deepseek_chat_client.gui import create_and_run_gui
        create_and_run_gui()
        
    except KeyboardInterrupt:
        print("\n用户中断，退出程序")
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
