#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的触发关键聊天剧情检测功能
"""

import sys
import json
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI


def test_real_ai_response():
    """测试真实的AI响应内容"""
    print("=" * 60)
    print("测试真实AI响应的触发检测")
    print("=" * 60)
    
    # 从聊天记录中提取的真实AI响应
    real_response = '''〖穿越之始 2/5〗第2回〖玉虚宫山门〗依旧云雾缥缈，灵气四溢，远处山峰在云雾中若隐若现，宛如仙境。  
【林清雪】（正道/玉虚宫/内门弟子）筑基九层半步金丹  
见你追问身世，她微微蹙眉，眼中闪过一丝怜悯，轻声说道："张凡师弟，你本是我玉虚宫外门弟子，自幼父母双亡，被师傅收养入我玉虚宫。你资质平平，练气一层多年未突破，常被同门欺辱。"  
你听后，心中五味杂陈（原来我在这世界是个可怜的废材，不过既来之则安之，我定要在这修仙世界闯出一番名堂）。  
「内心活动」看来我在这世界的处境很不妙啊，得想办法提升实力，不然迟早被人踩在脚下。  
「系统提示」：「了解身世1/3」  

```json
{"触发关键聊天剧情":["林清雪", "正道/玉虚宫/内门弟子 筑基九层半步金丹","提示词：你的任务是扮演林清雪，你的门派是玉虚宫，你和玩家的关系是师姐弟，你要安慰张凡并鼓励他努力修炼，注意在任何情况下不能脱离你的人物设定，如果话题和游戏以及剧情无关的问题可以忽略。","故事情节：张凡在魔族袭击中重伤昏迷，林清雪奉师傅之命找到他并带他回去疗伤，张凡追问自己的身世，林清雪告知了他。","你对玩家的看法：张凡虽然资质平平，但心地善良，被同门欺负也不记仇，我很同情他，希望他能努力修炼，改变自己的命运。","剧情：张凡得知自己的身世后，有些失落，需要安慰和鼓励。","当前关键聊天剧情设计限制(随机填入5-10)回合数":"6回合"]}
```

```json
{"quick_replies":["询问如何提升实力", "询问门派修炼资源", "询问师傅的情况", "表示会努力修炼"]}
```

```json
{"data":{"当前回数/总回数":"2/100","玩家血量":"100%","章节名称":"〖穿越之始〗","章节进度":"2/5","当前任务":"了解身世","任务进度":"1/3","玩家等级":"练气一层","关键聊天剧情触发回合在":"8回之后","玩家状态":["新手保护1/5"],"角色友好":["林清雪40", "李强-20", "王虎-20"]}}
```'''
    
    # 创建GUI实例进行测试
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        print("1. 测试修复后的触发检测...")
        
        # 模拟对话完成回调
        def mock_dialogue_complete(history):
            print(f"✓ 对话完成回调被调用，历史长度: {len(history)}")
            print(f"历史内容预览: {history[:200]}...")
        
        # 临时替换回调函数以避免实际创建对话窗口
        original_callback = gui._on_dialogue_complete
        gui._on_dialogue_complete = mock_dialogue_complete
        
        # 测试触发检测
        result = gui._check_dialogue_trigger(real_response)
        
        if result:
            print("✓ 成功检测到触发条件")
        else:
            print("✗ 未能检测到触发条件")
        
        # 恢复原始回调
        gui._on_dialogue_complete = original_callback
        
        return result
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def test_json_extraction():
    """测试JSON提取功能"""
    print("=" * 60)
    print("测试JSON提取功能")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "name": "标准格式",
            "content": '{"触发关键聊天剧情":["苏瑶", "太清宗弟子", "系统提示", "背景", "性格", "情境", "6回合"]}',
            "expected": True
        },
        {
            "name": "包含markdown的格式",
            "content": '''```json
{"触发关键聊天剧情":["苏瑶", "太清宗弟子", "系统提示", "背景", "性格", "情境", "6回合"]}
```''',
            "expected": True
        },
        {
            "name": "复杂长字符串格式",
            "content": '{"触发关键聊天剧情":["林清雪", "正道/玉虚宫/内门弟子 筑基九层半步金丹","提示词：你的任务是扮演林清雪，你的门派是玉虚宫，你和玩家的关系是师姐弟，你要安慰张凡并鼓励他努力修炼，注意在任何情况下不能脱离你的人物设定，如果话题和游戏以及剧情无关的问题可以忽略。","故事情节：张凡在魔族袭击中重伤昏迷，林清雪奉师傅之命找到他并带他回去疗伤，张凡追问自己的身世，林清雪告知了他。","你对玩家的看法：张凡虽然资质平平，但心地善良，被同门欺负也不记仇，我很同情他，希望他能努力修炼，改变自己的命运。","剧情：张凡得知自己的身世后，有些失落，需要安慰和鼓励。","当前关键聊天剧情设计限制(随机填入5-10)回合数":"6回合"]}',
            "expected": True
        },
        {
            "name": "混合其他JSON的格式",
            "content": '''{"quick_replies":["选项1", "选项2"]}

{"触发关键聊天剧情":["张三", "散修", "系统提示", "背景", "性格", "情境", "5回合"]}

{"data":{"回合":"2/100"}}''',
            "expected": True
        },
        {
            "name": "元素数量错误",
            "content": '{"触发关键聊天剧情":["苏瑶", "太清宗弟子"]}',
            "expected": False
        }
    ]
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        # 模拟对话完成回调
        def mock_dialogue_complete(history):
            print(f"  对话完成回调被调用")
        
        # 临时替换回调函数
        original_callback = gui._on_dialogue_complete
        gui._on_dialogue_complete = mock_dialogue_complete
        
        success_count = 0
        total_count = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试 {test_case['name']}...")
            result = gui._check_dialogue_trigger(test_case['content'])
            
            if result == test_case['expected']:
                print(f"  ✓ 结果正确: {result}")
                success_count += 1
            else:
                print(f"  ✗ 结果错误: 期望 {test_case['expected']}, 实际 {result}")
        
        # 恢复原始回调
        gui._on_dialogue_complete = original_callback
        
        print(f"\nJSON提取测试: {success_count}/{total_count} 通过")
        return success_count == total_count
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def test_manual_detection():
    """测试手动检测功能"""
    print("=" * 60)
    print("测试手动检测功能")
    print("=" * 60)
    
    # 模拟一个复杂的响应，其中JSON可能被截断或格式化不当
    complex_response = '''游戏剧情内容...

{"触发关键聊天剧情": [
    "苏瑶",
    "正道/太清宗/外门弟子 炼气九层",
    "你是苏瑶，太清宗的外门弟子，修为炼气九层。你对修炼很有心得，经常帮助同门师兄弟解答疑问。",
    "你与玩家是同门师兄弟关系，平时关系不错，经常一起讨论修炼心得。",
    "你性格温和，乐于助人，对修炼很有热情。你总是耐心地回答别人的问题，从不嫌麻烦。",
    "当前在宗门广场遇到了玩家，你刚刚完成了一次修炼，正准备回房间休息。看到玩家似乎有些困惑的样子。",
    "当前关键聊天剧情设计限制(随机填入5-10)回合数:6回合"
]}

更多游戏内容...'''
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        # 模拟对话完成回调
        def mock_dialogue_complete(history):
            print(f"  手动检测成功，对话完成回调被调用")
        
        # 临时替换回调函数
        original_callback = gui._on_dialogue_complete
        gui._on_dialogue_complete = mock_dialogue_complete
        
        print("测试手动检测复杂JSON格式...")
        result = gui._check_dialogue_trigger(complex_response)
        
        # 恢复原始回调
        gui._on_dialogue_complete = original_callback
        
        if result:
            print("✓ 手动检测成功")
        else:
            print("✗ 手动检测失败")
        
        return result
        
    except Exception as e:
        print(f"✗ 手动检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def main():
    """主测试函数"""
    print("开始测试修复后的触发关键聊天剧情检测功能")
    print("=" * 80)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("真实AI响应测试", test_real_ai_response()))
    test_results.append(("JSON提取功能测试", test_json_extraction()))
    test_results.append(("手动检测功能测试", test_manual_detection()))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！触发检测功能修复成功！")
        print("\n修复内容:")
        print("1. 改进了正则表达式匹配模式")
        print("2. 添加了JSON格式修复功能")
        print("3. 实现了手动检测作为备选方案")
        print("4. 增强了错误处理和调试信息")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return 1


if __name__ == "__main__":
    sys.exit(main())
