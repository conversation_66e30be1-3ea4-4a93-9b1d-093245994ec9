#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试保存进度和读取进度功能的脚本
"""

import sys
from pathlib import Path
import tkinter as tk
import json
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI
from deepseek_chat_client.config import config

def test_progress_features():
    """测试保存进度和读取进度功能"""
    print("=" * 60)
    print("测试保存进度和读取进度功能")
    print("=" * 60)
    
    try:
        # 创建GUI实例
        gui = ChatGUI()
        
        print("✓ GUI实例创建成功")
        
        # 测试保存进度和读取进度按钮是否存在
        print("\n检查按钮是否存在...")
        
        # 模拟一些聊天内容
        test_content = """第1天（早上）

意识边疆的黎明时分，天空中弥漫着淡淡的电磁雾霾。你站在塔克拉玛干"女娲城"（人类）的观察台上，远眺着这个被三大阵营撕裂的世界。

今天是2077年7月3日，距离AI阵营宣布的"硅基生命进化"最后通牒还有97天。整个世界都在屏息等待着什么...

你是一名隶属于人类阵营的特工，代号"守望者"。刚刚收到来自"认知安全联合委员会"（人类）的紧急任务简报。

[2025-07-18 23:15:12] 玩家:
新游戏开始

[2025-07-18 23:15:15] 游戏:
第1天（早上）

意识边疆的黎明时分，天空中弥漫着淡淡的电磁雾霾。你站在塔克拉玛干"女娲城"（人类）的观察台上，远眺着这个被三大阵营撕裂的世界。

今天是2077年7月3日，距离AI阵营宣布的"硅基生命进化"最后通牒还有97天。整个世界都在屏息等待着什么...

你是一名隶属于人类阵营的特工，代号"守望者"。刚刚收到来自"认知安全联合委员会"（人类）的紧急任务简报。

[2025-07-18 23:15:20] 玩家:
接受任务

[2025-07-18 23:15:25] 游戏:
你毫不犹豫地接受了任务。通讯器中传来指挥官冷静而坚定的声音：

"守望者，情况紧急。我们的间谍卫星发现，AI阵营在北极冰盖下的反光子计算中心出现异常活动。根据《燧人氏》生物计算机（人类）的分析，他们可能正在加速'种子计划'的进程。"

"你的任务是潜入缓冲地带，与我们的内线'白鸽'接头，获取更多情报。记住，这次行动关系到人类文明的存亡。"

就在这时，一个身影悄然出现在你身后...
"""
        
        # 将测试内容插入到消息显示区域
        gui.message_text.config(state=tk.NORMAL)
        gui.message_text.insert(tk.END, test_content)
        gui.message_text.config(state=tk.DISABLED)
        
        print("✓ 已插入测试聊天内容")
        
        # 模拟快捷回复按钮
        test_quick_replies = ["接受任务", "了解更多", "查看装备", "联系总部"]
        gui._show_quick_reply_buttons(test_quick_replies)
        
        print("✓ 已设置测试快捷回复按钮")
        
        # 设置一些模型参数
        gui.current_model.set("deepseek-reasoner")
        gui.temperature.set(1.2)
        gui.max_tokens.set(32768)
        gui.current_system_prompt_file.set("意识边疆历史故事背景.txt")
        gui.current_system_prompt_content = "测试系统提示词内容"
        
        print("✓ 已设置测试模型参数")
        
        # 测试保存进度功能
        print("\n测试保存进度功能...")
        
        # 检查progress文件夹是否会被创建
        progress_dir = config.project_root / "progress"
        if progress_dir.exists():
            print(f"  progress文件夹已存在: {progress_dir}")
        else:
            print(f"  progress文件夹不存在，将在保存时创建")
        
        # 调用保存进度方法
        try:
            gui._save_progress()
            print("✓ 保存进度功能调用成功")
        except Exception as e:
            print(f"✗ 保存进度功能调用失败: {e}")
            return False
        
        # 检查是否生成了进度文件
        if progress_dir.exists():
            progress_files = list(progress_dir.glob("progress_*.json"))
            if progress_files:
                print(f"✓ 找到 {len(progress_files)} 个进度文件:")
                for file in progress_files:
                    print(f"  - {file.name} ({file.stat().st_size} 字节)")
                
                # 读取最新文件的内容
                latest_file = max(progress_files, key=lambda f: f.stat().st_mtime)
                print(f"\n最新进度文件内容预览 ({latest_file.name}):")
                print("-" * 40)
                
                with open(latest_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                
                # 验证文件结构
                required_fields = ["version", "saved_at", "session_info", "model_settings", "messages", "quick_replies"]
                for field in required_fields:
                    if field in progress_data:
                        print(f"  ✓ {field}: {type(progress_data[field])}")
                    else:
                        print(f"  ✗ 缺少字段: {field}")
                
                print(f"  - 消息数量: {len(progress_data.get('messages', []))}")
                print(f"  - 快捷回复数量: {len(progress_data.get('quick_replies', []))}")
                print(f"  - 模型设置: {progress_data.get('model_settings', {})}")
                
                # 测试读取进度功能
                print("\n测试读取进度功能...")
                
                # 清空当前内容
                gui.message_text.config(state=tk.NORMAL)
                gui.message_text.delete(1.0, tk.END)
                gui.message_text.config(state=tk.DISABLED)
                gui._hide_quick_reply_buttons()
                
                print("✓ 已清空当前内容")
                
                # 测试验证进度数据
                if gui._validate_progress_data(progress_data):
                    print("✓ 进度数据验证通过")
                    
                    # 测试恢复进度数据
                    try:
                        gui._restore_progress_data(progress_data)
                        print("✓ 进度数据恢复成功")
                        
                        # 验证恢复结果
                        restored_content = gui.message_text.get(1.0, tk.END)
                        if "意识边疆的黎明时分" in restored_content:
                            print("✓ 消息内容恢复正确")
                        else:
                            print("✗ 消息内容恢复失败")
                        
                        # 检查快捷回复按钮是否恢复
                        if hasattr(gui, 'quick_reply_buttons') and gui.quick_reply_buttons:
                            print(f"✓ 快捷回复按钮已恢复: {len(gui.quick_reply_buttons)-1} 个")
                        else:
                            print("✗ 快捷回复按钮未恢复")
                        
                        # 检查模型设置是否恢复
                        if gui.current_model.get() == "deepseek-reasoner":
                            print("✓ 模型设置恢复正确")
                        else:
                            print(f"✗ 模型设置恢复错误: {gui.current_model.get()}")
                        
                    except Exception as e:
                        print(f"✗ 进度数据恢复失败: {e}")
                        return False
                        
                else:
                    print("✗ 进度数据验证失败")
                    return False
                
            else:
                print("✗ 未找到进度文件")
                return False
        else:
            print("✗ progress文件夹未创建")
            return False
        
        # 关闭GUI
        gui.root.destroy()
        
        print("\n" + "=" * 60)
        print("✓ 所有功能测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_progress_features()
    sys.exit(0 if success else 1)
