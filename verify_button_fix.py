#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证按钮宽度修复效果
快速验证修复是否成功
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def verify_fix():
    """验证修复效果"""
    print("=" * 60)
    print("验证按钮宽度修复效果")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏主窗口
        
        # 测试图片中的实际文本
        test_texts = [
            "恭敬行礼: 陛下愿听长老指点",
            "营房后退: 如何证明您身份？",
            "直接询问: 三道试炼是什么？",
            "展示玉佩: 此物实众发光是何缘由？"
        ]
        
        print("修复验证结果:")
        print("-" * 40)
        
        all_good = True
        
        for i, text in enumerate(test_texts, 1):
            # 计算按钮字符宽度
            char_width = gui._calculate_button_width_chars(text)
            
            # 检查是否足够显示完整文本
            text_width = gui._calculate_text_width(text)
            is_sufficient = char_width >= text_width
            
            status = "✅ 足够" if is_sufficient else "❌ 不足"
            
            print(f"{i}. '{text}'")
            print(f"   文本宽度: {text_width} 字符")
            print(f"   按钮宽度: {char_width} 字符")
            print(f"   显示状态: {status}")
            print()
            
            if not is_sufficient:
                all_good = False
        
        gui.root.destroy()
        
        print("=" * 60)
        if all_good:
            print("🎉 修复验证成功！所有按钮都能显示完整文本")
            print("\n修复要点:")
            print("✅ 使用字符宽度而不是像素宽度")
            print("✅ 正确计算中英文字符宽度")
            print("✅ 设置合适的最小和最大宽度")
            print("✅ 为按钮添加适当的内边距")
        else:
            print("❌ 修复验证失败，部分按钮仍然无法显示完整文本")
            print("请检查计算逻辑是否正确")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n" + "=" * 60)
    print("修复总结")
    print("=" * 60)
    
    print("问题根源:")
    print("❌ ttk.Button的width参数是字符宽度，不是像素宽度")
    print("❌ 之前错误地使用像素计算结果作为字符宽度")
    
    print("\n修复方案:")
    print("✅ 创建_calculate_button_width_chars()方法")
    print("✅ 正确计算字符宽度（中文2字符，英文1字符）")
    print("✅ 设置合理的最小宽度(8)和最大宽度(40)")
    print("✅ 添加适当的内边距(4字符)")
    
    print("\n预期效果:")
    print("🎯 按钮能够显示完整的文本内容")
    print("🎯 按钮宽度根据文本长度动态调整")
    print("🎯 界面保持美观和整洁")
    print("🎯 工具提示功能继续工作")

def main():
    """主函数"""
    print("快捷回复按钮宽度修复验证")
    
    # 运行验证
    success = verify_fix()
    
    # 显示总结
    show_fix_summary()
    
    print("\n" + "=" * 60)
    print("验证结论")
    print("=" * 60)
    
    if success:
        print("🎉 修复成功！")
        print("\n现在您可以:")
        print("1. 重启应用程序")
        print("2. 测试快捷回复按钮")
        print("3. 确认文本显示完整")
        print("4. 享受改进后的用户体验")
    else:
        print("❌ 修复需要进一步调整")
        print("\n建议:")
        print("1. 检查计算逻辑")
        print("2. 调整宽度参数")
        print("3. 测试不同文本长度")
        print("4. 验证字符宽度计算")

if __name__ == "__main__":
    main()
