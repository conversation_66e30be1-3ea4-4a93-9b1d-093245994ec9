#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证火山引擎模型Token配置修复的测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_token_configurations():
    """测试Token配置修复"""
    print("=" * 80)
    print("火山引擎模型Token配置修复验证测试")
    print("=" * 80)
    
    try:
        # 1. 导入模块
        print("\n1. 导入模块...")
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        from deepseek_chat_client.volcengine_client import VolcengineAPIClient
        print("✓ 模块导入成功")
        
        # 2. 初始化客户端
        print("\n2. 初始化客户端...")
        unified_client = UnifiedAPIClient()
        volcengine_client = VolcengineAPIClient()
        print("✓ 客户端初始化成功")
        
        # 3. 验证官方Token限制配置
        print("\n3. 验证官方Token限制配置...")
        
        # 官方限制数据（用于验证）
        official_limits = {
            "doubao-seed-1-6-250615": 16384,       # 16k
            "doubao-seed-1-6-thinking-250715": 65536,  # 64k
            "doubao-seed-1-6-flash-250715": 16384,     # 16k
            "doubao-1-5-pro-32k-250115": 12288        # 12k
        }
        
        all_correct = True
        
        for model, expected_limit in official_limits.items():
            # 测试统一客户端
            unified_limit = unified_client.get_recommended_max_tokens(model)
            
            # 测试火山引擎客户端
            volcengine_config = volcengine_client.get_model_config(model)
            volcengine_limit = volcengine_config.get("max_tokens", 0)
            
            if unified_limit == expected_limit and volcengine_limit == expected_limit:
                print(f"  ✓ {model}: {unified_limit:,} tokens (正确)")
            else:
                print(f"  ✗ {model}: 统一客户端={unified_limit:,}, 火山引擎={volcengine_limit:,}, 期望={expected_limit:,}")
                all_correct = False
        
        if all_correct:
            print("✓ 所有模型Token限制配置正确")
        else:
            print("✗ 存在Token限制配置错误")
            return False
        
        # 4. 测试Token步进序列
        print("\n4. 测试Token步进序列...")
        token_steps = [512, 1024, 2048, 4096, 8192, 16384, 32768, 65536]
        print(f"  步进序列: {' → '.join([f'{t:,}' for t in token_steps])}")
        
        # 验证步进序列的合理性
        for i in range(len(token_steps) - 1):
            if token_steps[i+1] != token_steps[i] * 2:
                print(f"  ✗ 步进序列不是2的幂次: {token_steps[i]} → {token_steps[i+1]}")
                return False
        
        print("  ✓ 步进序列配置正确（2的幂次递增）")
        
        # 5. 测试安全Token值计算
        print("\n5. 测试安全Token值计算...")
        
        test_cases = [
            # (模型, 请求Token, 期望结果)
            ("doubao-seed-1-6-250615", 20000, 16384),      # 超限调整
            ("doubao-seed-1-6-thinking-250715", 70000, 65536),  # 超限调整
            ("doubao-seed-1-6-flash-250715", 8192, 8192),       # 正常值
            ("doubao-1-5-pro-32k-250115", 15000, 12288),        # 超限调整
            ("doubao-seed-1-6-250615", 100, 512),               # 低于最小值调整
        ]
        
        for model, requested, expected in test_cases:
            safe_value = unified_client.get_safe_token_value(model, requested)
            if safe_value == expected:
                print(f"  ✓ {model}: {requested:,} → {safe_value:,} (正确)")
            else:
                print(f"  ✗ {model}: {requested:,} → {safe_value:,}, 期望 {expected:,}")
                return False
        
        print("✓ 安全Token值计算正确")
        
        # 6. 测试模型详细配置
        print("\n6. 测试模型详细配置...")
        
        for model in official_limits.keys():
            config = volcengine_client.get_model_config(model)
            
            required_fields = ["name", "description", "max_tokens", "context_window", "max_input_tokens", "price"]
            missing_fields = []
            
            for field in required_fields:
                if field not in config:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"  ✗ {model}: 缺少字段 {missing_fields}")
                return False
            else:
                print(f"  ✓ {model}: 配置完整")
                print(f"    - 名称: {config['name']}")
                print(f"    - 最大生成Token: {config['max_tokens']:,}")
                print(f"    - 上下文窗口: {config['context_window']:,}")
                print(f"    - 最大输入Token: {config['max_input_tokens']:,}")
                if 'max_thinking_tokens' in config:
                    print(f"    - 最大思考Token: {config['max_thinking_tokens']:,}")
        
        print("✓ 所有模型配置完整")
        
        # 7. 测试API调用参数验证
        print("\n7. 测试API调用参数验证...")
        
        # 模拟API调用参数
        for model, max_limit in official_limits.items():
            # 测试正常范围内的Token值
            safe_tokens = unified_client.get_safe_token_value(model, max_limit - 1000)
            if safe_tokens <= max_limit:
                print(f"  ✓ {model}: 正常Token值 {safe_tokens:,} ≤ 限制 {max_limit:,}")
            else:
                print(f"  ✗ {model}: Token值 {safe_tokens:,} > 限制 {max_limit:,}")
                return False
        
        print("✓ API调用参数验证通过")
        
        # 8. 生成配置总结
        print("\n8. 配置总结...")
        print("\n  📊 火山引擎模型Token限制:")
        print("  " + "="*60)
        print(f"  {'模型名称':<35} {'最大Token':<12} {'上下文':<10}")
        print("  " + "-"*60)
        
        for model in official_limits.keys():
            config = volcengine_client.get_model_config(model)
            name = config.get('name', model)
            max_tokens = config.get('max_tokens', 0)
            context = config.get('context_window', 0)
            
            print(f"  {name:<35} {max_tokens:,}K{'':<6} {context//1024:,}K")
        
        print("  " + "="*60)
        
        # 9. 验证DeepSeek模型配置（确保兼容性）
        print("\n9. 验证DeepSeek模型配置...")
        
        deepseek_models = {
            "deepseek-chat": 32768,
            "deepseek-reasoner": 65536
        }
        
        for model, expected in deepseek_models.items():
            actual = unified_client.get_recommended_max_tokens(model)
            if actual == expected:
                print(f"  ✓ {model}: {actual:,} tokens")
            else:
                print(f"  ✗ {model}: {actual:,} tokens, 期望 {expected:,}")
                return False
        
        print("✓ DeepSeek模型配置正确")
        
        print("\n" + "="*80)
        print("🎉 所有Token配置修复验证通过！")
        print("="*80)
        
        print("\n💡 修复总结:")
        print("  ✅ 火山引擎模型Token限制已按官方API文档修复")
        print("  ✅ Token步进调整序列已实现 (512→1K→2K→4K→8K→16K→32K→64K)")
        print("  ✅ 模型特定的最大Token限制保护已启用")
        print("  ✅ 安全Token值计算功能正常工作")
        print("  ✅ API调用参数验证机制已完善")
        print("  ✅ DeepSeek和火山引擎模型兼容性保持良好")
        
        print("\n🚀 现在可以安全启动GUI应用，不会再出现Token超限错误！")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = test_token_configurations()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
