#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证豆包1.6深度思考模型修复效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_volcengine_client_thinking_support():
    """测试火山引擎客户端的thinking支持"""
    print("=" * 60)
    print("测试火山引擎客户端thinking支持")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.volcengine_client import VolcengineAPIClient
        
        # 创建客户端实例
        client = VolcengineAPIClient()
        
        # 测试thinking模式判断方法
        doubao_models = [
            "doubao-seed-1-6-250615",
            "doubao-seed-1-6-thinking-250715", 
            "doubao-seed-1-6-flash-250715"
        ]
        
        non_doubao_models = [
            "deepseek-reasoner",
            "deepseek-chat",
            "doubao-1-5-pro-32k-250115"
        ]
        
        print("1. 测试豆包模型识别...")
        for model in doubao_models:
            result = client._is_doubao_thinking_model(model)
            status = "✅" if result else "❌"
            print(f"  {status} {model}: {result}")
        
        print("\n2. 测试非豆包模型识别...")
        for model in non_doubao_models:
            result = client._is_doubao_thinking_model(model)
            status = "✅" if not result else "❌"
            print(f"  {status} {model}: {result}")
        
        print("\n3. 测试chat_completion方法签名...")
        import inspect
        sig = inspect.signature(client.chat_completion)
        params = list(sig.parameters.keys())
        
        if 'thinking_mode' in params:
            print("  ✅ thinking_mode参数已添加")
        else:
            print("  ❌ thinking_mode参数缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_unified_client_thinking_support():
    """测试统一客户端的thinking支持"""
    print("\n" + "=" * 60)
    print("测试统一客户端thinking支持")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        
        # 创建统一客户端实例
        client = UnifiedAPIClient()
        
        print("1. 测试chat_completion方法签名...")
        import inspect
        sig = inspect.signature(client.chat_completion)
        params = list(sig.parameters.keys())
        
        if 'thinking_mode' in params:
            print("  ✅ thinking_mode参数已添加")
        else:
            print("  ❌ thinking_mode参数缺失")
        
        print("\n2. 测试模型提供商识别...")
        test_models = [
            ("doubao-seed-1-6-250615", "volcengine"),
            ("deepseek-reasoner", "deepseek"),
            ("deepseek-chat", "deepseek")
        ]
        
        for model, expected_provider in test_models:
            provider = client.get_provider_for_model(model)
            status = "✅" if provider == expected_provider else "❌"
            print(f"  {status} {model}: {provider} (期望: {expected_provider})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_thinking_mode_support():
    """测试GUI的thinking模式支持"""
    print("\n" + "=" * 60)
    print("测试GUI thinking模式支持")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()
        
        print("1. 测试_get_thinking_mode_for_model方法...")
        
        # 测试豆包模型
        doubao_models = [
            "doubao-seed-1-6-250615",
            "doubao-seed-1-6-thinking-250715", 
            "doubao-seed-1-6-flash-250715"
        ]
        
        for model in doubao_models:
            mode = gui._get_thinking_mode_for_model(model)
            status = "✅" if mode == "auto" else "❌"
            print(f"  {status} {model}: {mode} (期望: auto)")
        
        # 测试非豆包模型
        other_models = [
            "deepseek-reasoner",
            "deepseek-chat"
        ]
        
        for model in other_models:
            mode = gui._get_thinking_mode_for_model(model)
            status = "✅" if mode == "disabled" else "❌"
            print(f"  {status} {model}: {mode} (期望: disabled)")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dialogue_window_thinking_support():
    """测试对话窗口的thinking支持"""
    print("\n" + "=" * 60)
    print("测试对话窗口thinking支持")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from deepseek_chat_client.dialogue_window import DialogueWindow
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()
        
        # 创建对话窗口实例
        dialogue = DialogueWindow(
            parent_gui=gui,
            npc_name="测试NPC",
            npc_identity="测试身份",
            npc_system_prompt="测试提示词",
            background_info="测试背景",
            personality="测试性格",
            context="测试情境",
            turn_limit=3,
            on_dialogue_complete=lambda x, y: None
        )
        
        print("1. 测试_get_thinking_mode_for_model方法...")
        
        # 测试豆包模型
        doubao_models = [
            "doubao-seed-1-6-250615",
            "doubao-seed-1-6-thinking-250715", 
            "doubao-seed-1-6-flash-250715"
        ]
        
        for model in doubao_models:
            mode = dialogue._get_thinking_mode_for_model(model)
            status = "✅" if mode == "auto" else "❌"
            print(f"  {status} {model}: {mode} (期望: auto)")
        
        print("\n2. 测试_append_thinking_content方法...")
        if hasattr(dialogue, '_append_thinking_content'):
            print("  ✅ _append_thinking_content方法存在")
        else:
            print("  ❌ _append_thinking_content方法缺失")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🧠 开始验证豆包1.6深度思考模型修复效果")
    print("=" * 100)
    
    print("📋 验证内容：")
    print("1. 火山引擎客户端thinking参数支持")
    print("2. 统一客户端thinking模式路由")
    print("3. GUI界面thinking模式判断")
    print("4. 对话窗口thinking内容显示支持")
    print()
    
    # 运行测试
    tests = [
        ("火山引擎客户端thinking支持", test_volcengine_client_thinking_support),
        ("统一客户端thinking支持", test_unified_client_thinking_support),
        ("GUI thinking模式支持", test_gui_thinking_mode_support),
        ("对话窗口thinking支持", test_dialogue_window_thinking_support),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 100)
    print("📊 验证结果汇总")
    print("=" * 100)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 豆包1.6深度思考模型支持修复成功！")
        print("\n✨ 修复效果：")
        print("  ✅ 火山引擎客户端正确支持thinking参数")
        print("  ✅ 统一客户端智能路由thinking模式")
        print("  ✅ GUI界面自动判断thinking模式")
        print("  ✅ 对话窗口完整支持思考内容显示")
        
        print("\n🚀 现在可以正常使用豆包1.6深度思考模型了！")
        print("\n💡 使用建议：")
        print("  • 选择豆包1.6系列模型（doubao-seed-1-6-*）")
        print("  • 系统会自动使用'auto'思考模式")
        print("  • 思考内容会以灰色斜体显示")
        print("  • 支持主聊天、对话窗口、主线剧情衔接等所有场景")
        
        return 0
    else:
        print("\n❌ 部分验证失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    sys.exit(main())
