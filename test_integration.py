#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试火山引擎集成到DeepSeek聊天客户端的脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_integration():
    """测试集成功能"""
    print("=" * 70)
    print("测试火山引擎集成到DeepSeek聊天客户端")
    print("=" * 70)
    
    try:
        # 1. 测试导入
        print("\n1. 测试模块导入...")
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        from deepseek_chat_client.volcengine_client import VolcengineAPIClient
        from deepseek_chat_client.config import config
        print("✓ 所有模块导入成功")
        
        # 2. 测试统一客户端初始化
        print("\n2. 测试统一客户端初始化...")
        unified_client = UnifiedAPIClient()
        print("✓ 统一客户端初始化成功")
        
        # 3. 测试可用模型列表
        print("\n3. 测试可用模型列表...")
        available_models = unified_client.get_available_models()
        print(f"✓ 找到 {len(available_models)} 个可用模型:")
        for model_id, model_name in available_models.items():
            provider = unified_client.get_provider_for_model(model_id)
            price = unified_client.get_model_price(model_id)
            print(f"  - {model_id}: {model_name} (提供商: {provider})")
        
        # 4. 测试提供商状态
        print("\n4. 测试提供商状态...")
        provider_status = unified_client.get_provider_status()
        for provider, status in provider_status.items():
            available = "✓ 可用" if status["available"] else "✗ 不可用"
            model_count = status["model_count"]
            print(f"  {provider}: {available} ({model_count} 个模型)")
        
        # 5. 测试连接
        print("\n5. 测试API连接...")
        connection_results = unified_client.test_all_connections()
        for provider, (success, message) in connection_results.items():
            status = "✓ 成功" if success else "✗ 失败"
            print(f"  {provider}: {status} - {message}")
        
        # 6. 测试模型信息
        print("\n6. 测试模型详细信息...")
        test_models = [
            "deepseek-chat",
            "deepseek-reasoner", 
            "doubao-seed-1-6-flash-250715",
            "doubao-1-5-pro-32k-250115"
        ]
        
        for model in test_models:
            if model in available_models:
                info = unified_client.get_model_info(model)
                print(f"  {model}:")
                print(f"    名称: {info.get('name', 'N/A')}")
                print(f"    提供商: {info.get('provider', 'N/A')}")
                print(f"    价格: {info.get('price', 'N/A')}")
                print(f"    最大Token: {info.get('max_tokens', 'N/A')}")
                print(f"    描述: {info.get('description', 'N/A')}")
        
        # 7. 测试GUI集成（不启动GUI）
        print("\n7. 测试GUI集成...")
        try:
            from deepseek_chat_client.gui import ChatGUI
            print("✓ GUI模块导入成功")
            print("  注意: GUI未启动，仅测试导入和初始化")
        except Exception as e:
            print(f"✗ GUI集成测试失败: {e}")
        
        # 8. 总结
        print("\n" + "=" * 70)
        print("集成测试总结")
        print("=" * 70)
        
        deepseek_available = provider_status["deepseek"]["available"]
        volcengine_available = provider_status["volcengine"]["available"]
        
        print(f"\n📊 提供商状态:")
        print(f"  DeepSeek: {'✓ 可用' if deepseek_available else '✗ 不可用'}")
        print(f"  火山引擎: {'✓ 可用' if volcengine_available else '✗ 不可用'}")
        
        print(f"\n📋 模型统计:")
        print(f"  总模型数: {len(available_models)}")
        print(f"  DeepSeek模型: {provider_status['deepseek']['model_count']}")
        print(f"  火山引擎模型: {provider_status['volcengine']['model_count']}")
        
        if volcengine_available:
            print(f"\n🎉 火山引擎集成成功!")
            print("  您现在可以在聊天客户端中使用以下火山引擎模型:")
            volcengine_models = [m for m in available_models.keys() if unified_client.get_provider_for_model(m) == "volcengine"]
            for model in volcengine_models:
                price = unified_client.get_model_price(model)
                print(f"    - {model} (价格: {price})")
        else:
            print(f"\n⚠️ 火山引擎API不可用")
            print("  请检查.env文件中的volcengine_API_KEY配置")
        
        print(f"\n💡 使用建议:")
        if deepseek_available and volcengine_available:
            print("  1. 根据任务类型选择合适的模型")
            print("  2. 考虑价格因素进行模型选择")
            print("  3. 使用快速模型进行简单任务，使用强大模型进行复杂任务")
        elif deepseek_available:
            print("  1. 当前只有DeepSeek模型可用")
            print("  2. 配置火山引擎API密钥以获得更多模型选择")
        elif volcengine_available:
            print("  1. 当前只有火山引擎模型可用")
            print("  2. 配置DeepSeek API密钥以获得更多模型选择")
        else:
            print("  1. 请配置至少一个API密钥")
            print("  2. 检查网络连接和API密钥有效性")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = test_integration()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
