#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试独处聊天上下文传递修复功能
"""

import sys
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_dialogue_context_fix():
    """测试独处聊天上下文传递修复功能"""
    print("=" * 80)
    print("独处聊天上下文传递修复功能测试")
    print("=" * 80)
    
    try:
        # 1. 导入模块
        print("\n1. 导入模块...")
        from deepseek_chat_client.gui import ChatGUI
        from deepseek_chat_client.dialogue_window import DialogueWindow
        import tkinter as tk
        print("✓ 模块导入成功")
        
        # 2. 创建GUI实例
        print("\n2. 创建GUI实例...")
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏主窗口
        print("✓ GUI实例创建成功")
        
        # 3. 测试未完成对话历史编译
        print("\n3. 测试未完成对话历史编译...")
        
        # 创建模拟的对话窗口
        test_root = tk.Tk()
        test_root.withdraw()
        
        # 模拟对话数据
        npc_name = "苏瑶"
        npc_identity = "正道/太清宗/内门核心弟子 筑基九层半步金丹"
        turn_limit = 5

        # 构造NPC数据数组（DialogueWindow需要7个元素）
        npc_data = [
            npc_name,  # NPC名字
            npc_identity,  # NPC身份信息
            "你是一位修仙门派的师姐",  # 系统提示词
            "苏瑶是太清宗的内门核心弟子",  # NPC背景
            "温和友善，乐于助人",  # NPC性格
            "在宗门内遇到了师弟前来请教",  # 对话上下文
            f"当前关键聊天剧情设计限制(随机填入5-10)回合数:{turn_limit}回合"  # 回合限制
        ]

        # 创建对话窗口实例
        dialogue_window = DialogueWindow(
            parent_gui=gui,  # 使用主GUI实例
            npc_data=npc_data,
            on_dialogue_complete=None  # 暂时不设置回调
        )
        
        # 模拟部分对话历史
        dialogue_window.dialogue_history = [
            {
                "role": "assistant",
                "content": "师弟，你来找我有什么事吗？",
                "timestamp": "2025-01-01 10:00:00"
            },
            {
                "role": "user", 
                "content": "师姐，我想请教一些修炼上的问题。",
                "timestamp": "2025-01-01 10:00:30"
            },
            {
                "role": "assistant",
                "content": "当然可以，你有什么不明白的地方？",
                "timestamp": "2025-01-01 10:01:00"
            },
            {
                "role": "user",
                "content": "我在练习基础剑法时总是感觉力不从心。",
                "timestamp": "2025-01-01 10:01:30"
            }
        ]
        dialogue_window.current_turn = 2  # 进行了2回合，还有3回合未完成
        
        # 测试编译未完成对话历史
        compiled_history = dialogue_window._compile_incomplete_dialogue_history()
        
        print("编译的未完成对话历史:")
        print("-" * 60)
        print(compiled_history)
        print("-" * 60)
        
        # 验证编译结果
        expected_elements = [
            "=== 独处聊天记录（未完成）===",
            f"NPC: {npc_name}",
            f"身份: {npc_identity}",
            "对话进度: 2/5 回合（提前结束）",
            "玩家: 师姐，我想请教一些修炼上的问题。",
            f"{npc_name}: 当然可以，你有什么不明白的地方？",
            "--- 对话未完成说明 ---",
            "玩家提前结束了与【苏瑶】的对话。",
            "原计划进行 5 回合，实际进行了 2 回合。"
        ]
        
        missing_elements = []
        for element in expected_elements:
            if element not in compiled_history:
                missing_elements.append(element)
        
        if not missing_elements:
            print("✓ 未完成对话历史编译正确")
        else:
            print("✗ 未完成对话历史编译缺少元素:")
            for element in missing_elements:
                print(f"  - {element}")
            return False
        
        # 4. 测试未完成对话处理逻辑
        print("\n4. 测试未完成对话处理逻辑...")
        
        # 模拟回调函数
        callback_called = False
        callback_history = None
        callback_npc_name = None
        
        def mock_callback(history, npc):
            nonlocal callback_called, callback_history, callback_npc_name
            callback_called = True
            callback_history = history
            callback_npc_name = npc
            print(f"[MOCK] 回调函数被调用: NPC={npc}, 历史长度={len(history)}")
        
        # 设置回调函数
        dialogue_window.on_dialogue_complete = mock_callback
        dialogue_window.is_dialogue_active = True
        
        # 测试处理未完成对话
        dialogue_window._handle_incomplete_dialogue()
        
        if callback_called:
            print("✓ 未完成对话处理回调成功")
            print(f"  - 回调NPC名称: {callback_npc_name}")
            print(f"  - 回调历史长度: {len(callback_history) if callback_history else 0}")
            
            if callback_npc_name == npc_name and callback_history:
                print("✓ 回调参数正确")
            else:
                print("✗ 回调参数错误")
                return False
        else:
            print("✗ 未完成对话处理回调失败")
            return False
        
        # 5. 测试主聊天窗口的对话完成处理
        print("\n5. 测试主聊天窗口的对话完成处理...")
        
        # 创建模拟的历史管理器
        class MockHistoryManager:
            def __init__(self):
                self.messages = []
            
            def add_message(self, role, content):
                self.messages.append({"role": role, "content": content})
                print(f"[MOCK] 添加消息: {role}, 长度={len(content)}")
        
        # 替换GUI的历史管理器
        original_history_manager = gui.history_manager
        gui.history_manager = MockHistoryManager()
        
        # 模拟消息显示方法
        displayed_messages = []
        def mock_display_message(role, content):
            displayed_messages.append({"role": role, "content": content})
            print(f"[MOCK] 显示消息: {role}, 长度={len(content)}")
        
        original_display_message = gui._display_message
        gui._display_message = mock_display_message
        
        # 模拟衔接方法
        continuation_called = False
        continuation_npc = None
        continuation_type = None
        
        def mock_incomplete_continuation(npc):
            nonlocal continuation_called, continuation_npc, continuation_type
            continuation_called = True
            continuation_npc = npc
            continuation_type = "incomplete"
            print(f"[MOCK] 未完成对话衔接被调用: {npc}")
        
        def mock_main_continuation(npc):
            nonlocal continuation_called, continuation_npc, continuation_type
            continuation_called = True
            continuation_npc = npc
            continuation_type = "complete"
            print(f"[MOCK] 完成对话衔接被调用: {npc}")
        
        gui._trigger_incomplete_dialogue_continuation = mock_incomplete_continuation
        gui._trigger_main_story_continuation = mock_main_continuation
        
        # 测试未完成对话的处理
        print("  测试未完成对话处理...")
        gui._on_dialogue_complete(compiled_history, npc_name)
        
        if continuation_called and continuation_type == "incomplete":
            print("✓ 未完成对话衔接正确触发")
            print(f"  - 衔接类型: {continuation_type}")
            print(f"  - NPC名称: {continuation_npc}")
        else:
            print(f"✗ 未完成对话衔接触发错误: called={continuation_called}, type={continuation_type}")
            return False
        
        # 重置状态
        continuation_called = False
        continuation_npc = None
        continuation_type = None
        
        # 测试完成对话的处理
        print("  测试完成对话处理...")
        complete_history = "=== 独处聊天记录（已完成）===\n完整的对话内容..."
        gui._on_dialogue_complete(complete_history, npc_name)
        
        if continuation_called and continuation_type == "complete":
            print("✓ 完成对话衔接正确触发")
            print(f"  - 衔接类型: {continuation_type}")
            print(f"  - NPC名称: {continuation_npc}")
        else:
            print(f"✗ 完成对话衔接触发错误: called={continuation_called}, type={continuation_type}")
            return False
        
        # 6. 测试窗口关闭事件处理
        print("\n6. 测试窗口关闭事件处理...")
        
        # 重置对话窗口状态
        dialogue_window.is_dialogue_active = True
        dialogue_window.current_turn = 3  # 模拟进行了3回合
        
        # 模拟窗口关闭（跳过用户确认）
        original_messagebox = None
        try:
            import tkinter.messagebox as messagebox
            original_messagebox = messagebox.askyesno
            messagebox.askyesno = lambda title, message: True  # 模拟用户选择"是"
            
            # 重置回调状态
            callback_called = False
            callback_history = None
            
            # 模拟窗口关闭
            dialogue_window._on_window_close()
            
            if callback_called:
                print("✓ 窗口关闭时正确处理未完成对话")
                print(f"  - 传递的历史记录长度: {len(callback_history) if callback_history else 0}")
            else:
                print("✗ 窗口关闭时未能处理未完成对话")
                return False
                
        finally:
            # 恢复原始的messagebox
            if original_messagebox:
                messagebox.askyesno = original_messagebox
        
        # 清理
        gui.history_manager = original_history_manager
        gui._display_message = original_display_message
        test_root.destroy()
        gui.root.destroy()
        
        print("\n" + "=" * 80)
        print("🎉 所有独处聊天上下文传递修复功能测试通过！")
        print("=" * 80)
        
        print("\n📊 测试结果总结:")
        print("  ✅ 未完成对话历史编译: 正确")
        print("  ✅ 未完成对话处理逻辑: 正确")
        print("  ✅ 主聊天窗口对话完成处理: 正确")
        print("  ✅ 窗口关闭事件处理: 正确")
        
        print("\n💡 修复功能特性:")
        print("  🎯 自动检测: 窗口关闭时自动检测未完成的对话")
        print("  📝 上下文编译: 完整编译对话历史和状态信息")
        print("  🔄 自动传递: 自动将上下文传递给主聊天窗口")
        print("  🚀 流程衔接: 触发正确的主线剧情衔接逻辑")
        
        print("\n🎮 用户体验改进:")
        print("  1. 未完成对话不会导致主窗口失去响应")
        print("  2. 对话内容得到完整保存和传递")
        print("  3. 主线剧情能够基于对话内容继续发展")
        print("  4. 提供清晰的对话状态说明")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = test_dialogue_context_fix()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
