#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
触发关键聊天剧情系统简单测试
专注于核心功能测试，避免GUI复杂性
"""

import sys
import json
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_trigger_pattern_detection():
    """测试触发模式检测"""
    print("=" * 50)
    print("测试触发模式检测")
    print("=" * 50)
    
    # 模拟GUI中的检测逻辑
    def check_dialogue_trigger(content: str) -> bool:
        """检测是否触发关键聊天剧情"""
        patterns = [
            r'\{"触发关键聊天剧情"\s*:\s*(\[.*?\])\}',  # 标准格式
            r'\{[^{}]*"触发关键聊天剧情"\s*:\s*(\[.*?\])[^{}]*\}',  # 包含其他字段的格式
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    npc_data = json.loads(match)
                    if isinstance(npc_data, list) and len(npc_data) == 7:
                        return True, npc_data
                except json.JSONDecodeError:
                    continue
        
        return False, None
    
    # 测试用例
    test_cases = [
        {
            "name": "标准格式",
            "content": '{"触发关键聊天剧情": ["苏瑶", "正道/太清宗/外门弟子 炼气九层", "你是苏瑶", "背景信息", "性格描述", "对话情境", "6回合"]}',
            "expected": True
        },
        {
            "name": "包含其他字段",
            "content": '{"quick_replies": ["选项1"], "触发关键聊天剧情": ["张三", "散修", "系统提示", "背景", "性格", "情境", "5回合"], "other": "value"}',
            "expected": True
        },
        {
            "name": "多行格式",
            "content": '''
            {
                "触发关键聊天剧情": [
                    "李四",
                    "魔道修士",
                    "系统提示词",
                    "背景关系",
                    "性格特点",
                    "当前情境",
                    "8回合"
                ]
            }
            ''',
            "expected": True
        },
        {
            "name": "元素数量不足",
            "content": '{"触发关键聊天剧情": ["苏瑶", "太清宗弟子"]}',
            "expected": False
        },
        {
            "name": "无触发字段",
            "content": '{"quick_replies": ["选项1", "选项2"]}',
            "expected": False
        },
        {
            "name": "JSON格式错误",
            "content": '{"触发关键聊天剧情": ["苏瑶", "太清宗弟子", "系统提示", "背景", "性格", "情境", "6回合"}',  # 缺少结束括号
            "expected": False
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}...")
        result, npc_data = check_dialogue_trigger(test_case['content'])
        
        if result == test_case['expected']:
            print(f"  ✓ 结果正确: {result}")
            if result and npc_data:
                print(f"    NPC: {npc_data[0]}, 身份: {npc_data[1]}")
            success_count += 1
        else:
            print(f"  ✗ 结果错误: 期望 {test_case['expected']}, 实际 {result}")
    
    print(f"\n触发模式检测测试: {success_count}/{total_count} 通过")
    return success_count == total_count


def test_turn_limit_extraction():
    """测试回合限制提取"""
    print("=" * 50)
    print("测试回合限制提取")
    print("=" * 50)
    
    def extract_turn_limit(turn_limit_str: str) -> int:
        """从回合限制字符串中提取数字"""
        match = re.search(r'(\d+)回合', turn_limit_str)
        if match:
            return int(match.group(1))
        return 6  # 默认值
    
    test_cases = [
        ("6回合", 6),
        ("10回合", 10),
        ("当前关键聊天剧情设计限制(随机填入5-10)回合数:8回合", 8),
        ("限制5回合", 5),
        ("无效格式", 6),  # 默认值
        ("", 6),  # 空字符串，默认值
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (input_str, expected) in enumerate(test_cases, 1):
        print(f"\n{i}. 测试输入: '{input_str}'")
        result = extract_turn_limit(input_str)
        
        if result == expected:
            print(f"  ✓ 结果正确: {result}")
            success_count += 1
        else:
            print(f"  ✗ 结果错误: 期望 {expected}, 实际 {result}")
    
    print(f"\n回合限制提取测试: {success_count}/{total_count} 通过")
    return success_count == total_count


def test_npc_data_parsing():
    """测试NPC数据解析"""
    print("=" * 50)
    print("测试NPC数据解析")
    print("=" * 50)
    
    # 测试数据
    npc_data = [
        "苏瑶",  # NPC名字
        "正道/太清宗/外门弟子 炼气九层",  # NPC身份
        "你是苏瑶，太清宗的外门弟子，修为炼气九层。",  # 系统提示词
        "你与玩家是同门师兄弟关系，平时关系不错。",  # 背景关系
        "你性格温和，乐于助人，对修炼很有热情。",  # 性格动机
        "当前在宗门广场遇到了玩家，你刚刚完成了一次修炼。",  # 对话情境
        "当前关键聊天剧情设计限制(随机填入5-10)回合数:6回合"  # 回合限制
    ]
    
    print("1. 测试数据完整性...")
    if len(npc_data) == 7:
        print("  ✓ 数据包含7个元素")
    else:
        print(f"  ✗ 数据元素数量错误: {len(npc_data)}")
        return False
    
    print("2. 测试各字段解析...")
    
    # 解析各字段
    npc_name = npc_data[0]
    npc_identity = npc_data[1]
    system_prompt = npc_data[2]
    npc_background = npc_data[3]
    npc_personality = npc_data[4]
    dialogue_context = npc_data[5]
    turn_limit_str = npc_data[6]
    
    # 验证字段
    tests = [
        ("NPC名字", npc_name, "苏瑶"),
        ("NPC身份", npc_identity, "正道/太清宗/外门弟子 炼气九层"),
        ("系统提示词长度", len(system_prompt) > 0, True),
        ("背景信息长度", len(npc_background) > 0, True),
        ("性格描述长度", len(npc_personality) > 0, True),
        ("对话情境长度", len(dialogue_context) > 0, True),
        ("回合限制字符串", "回合" in turn_limit_str, True),
    ]
    
    success_count = 0
    for test_name, actual, expected in tests:
        if actual == expected:
            print(f"  ✓ {test_name}: {actual}")
            success_count += 1
        else:
            print(f"  ✗ {test_name}: 期望 {expected}, 实际 {actual}")
    
    print("3. 测试系统提示词构建...")
    combined_prompt = f"""
{system_prompt}

{npc_background}

{npc_personality}

{dialogue_context}

请注意：
1. 你正在扮演【{npc_name}】，身份是：{npc_identity}
2. 请保持角色一致性，用第一人称回应
3. 回应要符合角色的身份、性格和当前情况
4. 这是一个限制6回合的关键对话
5. 请用自然的对话方式回应，不要过于正式
""".strip()
    
    if len(combined_prompt) > 100:  # 基本长度检查
        print(f"  ✓ 系统提示词构建成功，长度: {len(combined_prompt)}")
        success_count += 1
    else:
        print(f"  ✗ 系统提示词构建失败，长度过短: {len(combined_prompt)}")
    
    print(f"\nNPC数据解析测试: {success_count}/{len(tests) + 1} 通过")
    return success_count == len(tests) + 1


def test_integration_scenario():
    """测试完整集成场景"""
    print("=" * 50)
    print("测试完整集成场景")
    print("=" * 50)
    
    # 模拟AI响应内容
    ai_response = '''
    〖第一章1/100〗第1回，〖青云山门〗青云山坐落在云雾缭绕的山峰之上，山门巍峨壮观。
    
    【张三】修为筑基初期，正在山门前犹豫不决。
    
    「内心活动」这里就是传说中的修仙门派吗？我真的能在这里学到仙法吗？
    
    {"触发关键聊天剧情": ["苏瑶", "正道/太清宗/外门弟子 炼气九层", "你是苏瑶，太清宗的外门弟子", "你与玩家是同门师兄弟关系", "你性格温和，乐于助人", "当前在宗门广场遇到了玩家", "当前关键聊天剧情设计限制(随机填入5-10)回合数:6回合"]}
    
    【系统提示】你可以选择与苏瑶进行深入对话。
    '''
    
    print("1. 测试从AI响应中检测触发...")
    
    # 检测触发
    patterns = [
        r'\{"触发关键聊天剧情"\s*:\s*(\[.*?\])\}',
        r'\{[^{}]*"触发关键聊天剧情"\s*:\s*(\[.*?\])[^{}]*\}',
    ]
    
    triggered = False
    npc_data = None
    
    for pattern in patterns:
        matches = re.findall(pattern, ai_response, re.DOTALL | re.IGNORECASE)
        for match in matches:
            try:
                parsed_data = json.loads(match)
                if isinstance(parsed_data, list) and len(parsed_data) == 7:
                    triggered = True
                    npc_data = parsed_data
                    break
            except json.JSONDecodeError:
                continue
        if triggered:
            break
    
    if triggered and npc_data:
        print(f"  ✓ 成功检测到触发，NPC: {npc_data[0]}")
    else:
        print("  ✗ 未能检测到触发")
        return False
    
    print("2. 测试NPC数据处理...")
    
    # 处理NPC数据
    npc_name = npc_data[0]
    npc_identity = npc_data[1]
    
    # 提取回合限制
    turn_limit_str = npc_data[6]
    match = re.search(r'(\d+)回合', turn_limit_str)
    turn_limit = int(match.group(1)) if match else 6
    
    print(f"  ✓ NPC名字: {npc_name}")
    print(f"  ✓ NPC身份: {npc_identity}")
    print(f"  ✓ 回合限制: {turn_limit}")
    
    print("3. 测试对话历史格式化...")
    
    # 模拟对话历史
    dialogue_history = [
        {"role": "user", "content": "你好，苏瑶师姐", "timestamp": "14:30:01"},
        {"role": "assistant", "content": "师弟你好，刚才修炼得如何？", "timestamp": "14:30:05"},
        {"role": "user", "content": "还不错，想请教一些问题", "timestamp": "14:30:10"},
        {"role": "assistant", "content": "当然可以，有什么问题尽管问", "timestamp": "14:30:15"},
    ]
    
    # 格式化对话历史
    history_lines = []
    history_lines.append(f"=== 触发关键聊天剧情记录 ===")
    history_lines.append(f"NPC: 【{npc_name}】（{npc_identity}）")
    history_lines.append(f"回合数: {len(dialogue_history)//2}/{turn_limit}")
    history_lines.append("=" * 40)
    history_lines.append("")
    
    for msg in dialogue_history:
        if msg["role"] == "user":
            history_lines.append(f"玩家 [{msg['timestamp']}]: {msg['content']}")
        elif msg["role"] == "assistant":
            history_lines.append(f"【{npc_name}】 [{msg['timestamp']}]: {msg['content']}")
        history_lines.append("")
    
    history_lines.append("=== 对话记录结束 ===")
    dialogue_summary = "\n".join(history_lines)
    
    if len(dialogue_summary) > 100:
        print(f"  ✓ 对话历史格式化成功，长度: {len(dialogue_summary)}")
    else:
        print(f"  ✗ 对话历史格式化失败")
        return False
    
    print("\n完整集成场景测试通过")
    return True


def main():
    """主测试函数"""
    print("开始触发关键聊天剧情系统简单测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("触发模式检测", test_trigger_pattern_detection()))
    test_results.append(("回合限制提取", test_turn_limit_extraction()))
    test_results.append(("NPC数据解析", test_npc_data_parsing()))
    test_results.append(("完整集成场景", test_integration_scenario()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！触发关键聊天剧情系统核心功能实现成功！")
        return 0
    else:
        print("❌ 部分测试失败，需要修复问题")
        return 1


if __name__ == "__main__":
    sys.exit(main())
