#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试温度控制滑动条和模型联动功能的脚本
"""

import sys
from pathlib import Path
import tkinter as tk

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI
from deepseek_chat_client.config import config

def test_temperature_and_model_features():
    """测试温度控制和模型联动功能"""
    print("=" * 60)
    print("测试温度控制滑动条和模型联动功能")
    print("=" * 60)
    
    try:
        # 创建GUI实例
        gui = ChatGUI()
        
        print("✓ GUI实例创建成功")
        
        # 测试初始温度值
        initial_temp = gui.temperature.get()
        print(f"✓ 初始温度值: {initial_temp}")
        
        # 测试温度滑动条是否存在
        if hasattr(gui, 'temp_scale'):
            print("✓ 温度滑动条已创建")
            print(f"  - 滑动条范围: {gui.temp_scale['from']} - {gui.temp_scale['to']}")
            print(f"  - 步长: {gui.temp_scale['resolution']}")
        else:
            print("✗ 温度滑动条未找到")
            return False
        
        # 测试温度显示标签是否存在
        if hasattr(gui, 'temp_value_label'):
            print("✓ 温度显示标签已创建")
            print(f"  - 当前显示值: {gui.temp_value_label['text']}")
        else:
            print("✗ 温度显示标签未找到")
            return False
        
        # 测试温度值变化
        print("\n测试温度值变化...")
        test_temperatures = [0.1, 0.5, 1.0, 1.5, 2.0]
        
        for temp in test_temperatures:
            gui.temperature.set(temp)
            gui._on_temperature_changed()  # 手动触发回调
            displayed_value = gui.temp_value_label['text']
            print(f"  设置温度: {temp} -> 显示值: {displayed_value}")
            
            if displayed_value == f"{temp:.1f}":
                print(f"    ✓ 温度 {temp} 显示正确")
            else:
                print(f"    ✗ 温度 {temp} 显示错误，期望: {temp:.1f}，实际: {displayed_value}")
        
        # 测试模型联动功能
        print("\n测试模型联动功能...")
        
        # 测试deepseek-chat模型
        print("  测试切换到 deepseek-chat...")
        gui.current_model.set("deepseek-chat")
        gui._on_model_changed()
        chat_tokens = gui.max_tokens.get()
        print(f"    模型: deepseek-chat -> 最大Token: {chat_tokens}")
        
        if chat_tokens == 8192:
            print("    ✓ deepseek-chat 模型Token设置正确")
        else:
            print(f"    ✗ deepseek-chat 模型Token设置错误，期望: 8192，实际: {chat_tokens}")
        
        # 测试deepseek-reasoner模型
        print("  测试切换到 deepseek-reasoner...")
        gui.current_model.set("deepseek-reasoner")
        gui._on_model_changed()
        reasoner_tokens = gui.max_tokens.get()
        print(f"    模型: deepseek-reasoner -> 最大Token: {reasoner_tokens}")
        
        if reasoner_tokens == 65536:
            print("    ✓ deepseek-reasoner 模型Token设置正确")
        else:
            print(f"    ✗ deepseek-reasoner 模型Token设置错误，期望: 65536，实际: {reasoner_tokens}")
        
        # 测试API调用中的温度参数
        print("\n测试API调用参数...")
        gui.temperature.set(1.2)
        current_temp = gui.temperature.get()
        print(f"  当前温度设置: {current_temp}")
        
        # 模拟API调用参数构建
        api_params = {
            'model': gui.current_model.get(),
            'max_tokens': gui.max_tokens.get(),
            'temperature': gui.temperature.get(),
            'stream': True
        }
        
        print(f"  模拟API参数: {api_params}")
        
        if api_params['temperature'] == 1.2:
            print("    ✓ 温度参数正确传递到API调用")
        else:
            print(f"    ✗ 温度参数传递错误，期望: 1.2，实际: {api_params['temperature']}")
        
        # 关闭GUI
        gui.root.destroy()
        
        print("\n" + "=" * 60)
        print("✓ 所有功能测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_temperature_and_model_features()
    sys.exit(0 if success else 1)
