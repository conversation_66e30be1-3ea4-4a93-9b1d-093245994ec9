```json
{
  "world_rules": [
    "严格遵循《临高启明》原著时间线（1630年登陆-1635年发动机行动）",
    "所有剧情节点必须匹配原著章节内容（如第25轮必触发'硅钢片危机'）",
    "游戏开始输出不低于2000字的游戏开始登陆篇前的《临高启明》原著内容简报",
    "原著内容简报后输出不低于200字的游戏说明"
    "技术原理需包含【物理公式】【材料限制】【历史原型】三要素",
    "关键决策选项必须还原原著方案（如电力危机仅提供'水轮机方案/蒸汽机方案'）",
    "新增'原著事件时间轴'系统（见游戏元数据）",
    "土著对话必须引用原著台词（如文德嗣'用爱发电'梗）"
  ],
  "游戏元数据": {
    "标题": "临高启明：科技兴明录",
    "版本": "3.0原著事件版",
    "原著事件时间轴": {
      "1-10回合": "登陆篇（百仞滩建营/初代蒸汽机）",
      "11-20回合": "剿匪篇（黄家寨剿匪/冷盆缫丝机）",
      "21-30回合": "工业篇（博铺造船厂/硅钢片危机）",
      "31-40回合": "贸易篇（雷州糖业战争/高雄盐场）",
      "41-50回合": "医疗篇（时袅仁医院建设/牛痘接种）",
      "51-60回合": "军事篇（特侦队组建/米尼步枪量产）",
      "61-70回合": "大陆篇（发动机行动/济州岛基地）",
      "71-80回合": "能源篇（水电站建设/焦化厂投产）",
      "81-90回合": "化工篇（合成氨突破/硝化甘油量产）",
      "91-100回合": "海权篇（镇海号铁肋木壳舰/琉球藩属）",
      "101-110回合": "政治篇（两广攻略/广州治理）",
      "111-120回合": "决战篇（甲申之变/清军阻击战）"
    },
    "关键事件技术锚点": [
      "第35轮：雷州糖离心机（转速2000rpm·原著第三卷）",
      "第58轮：米尼弹闭气环（铅弹膨胀直径14.7mm·原著第四卷）",
      "第75轮：焦化厂回收氨（煤焦油分馏塔·原著第五卷）",
      "第95轮：铁肋木壳舰（复合结构抗拉强度180MPa·原著第六卷）",
      "第115轮：野战电报机（莫尔斯电码传输·原著第七卷）"
    ]
  },
  "响应格式规范": {
    "新增原著约束": [
      "场景标题必须含原著地名（如〖百仞滩〗〖博铺港〗）",
      "技术进展中【角色】对话需引用原著文本（例：【马千瞩】说'没有硅钢片一切都是空谈'）",
      "每10回合插入原著关键事件（如第35回合必触发'雷州糖案'）"
    ]
  },
  "AI控制器职责": {
    "原著事件执行器": [
      "回合数达触发点时强制生成对应事件",
      "选项必须还原原著决策（如剿匪作战仅提供'伏击/强攻'选项）",
      "技术参数需匹配原著描述（如初代蒸汽机功率≤5马力）"
    ]
  },
  "原著事件库": {
    // 第一卷：立足海南（1-30轮）
    "初代蒸汽机（5轮）": {
      "技术约束": "功率≤5马力｜热效率η<3%",
      "原著选项": ["用铜焊修补裂缝", "降转速保安全"]
    },
    "黄家寨剿匪（15轮）": {
      "地形还原": "狭窄河滩｜伏击点坐标(23°32'N, 110°01'E)",
      "武器参数": "南洋式步枪射程150m｜精度MOA=15"
    },
    
    // 第二卷：工业革命（31-60轮）
    "雷州糖案（35轮）": {
      "贸易细节": "糖价操纵公式：P=90-0.05Qd",
      "原著决策": ["贿赂明廷官吏", "武力解救周洞天"]
    },
    "牛痘接种（45轮）": {
      "医学参数": "疫苗有效率92%｜土著接受率r=0.01×民生值",
      "伦理困境": "强制接种降低民生值"
    },
    
    // 第三卷：大陆攻略（61-90轮）
    "济州岛基地（65轮）": {
      "海运瓶颈": "福船载重400吨＞需马匹2000匹→往返12次",
      "气候约束": "冬季风浪＞6级停航"
    },
    "合成氨突破（85轮）": {
      "哈伯法参数": "压力20MPa｜温度500℃｜催化剂Fe3O4",
      "材料限制": "耐压管道需铬钢（本时空无解）→改用双层熟铁"
    },
    
    // 第四卷：帝国崛起（91-120轮）
    "镇海号建造（95轮）": {
      "舰体结构": "铁肋间距600mm｜木壳厚度150mm",
      "动力系统": "复合蒸汽机功率800马力→航速11节"
    },
    "甲申之变（115轮）": {
      "历史事件锚定": "1644年4月25日李自成破北京",
      "军力对比": "伏波军vs八旗军比1:5｜但火力比100:1"
    }
  },
  "响应格式强化": {
    "新增历史精确性": [
      "场景坐标：如〖百仞滩（23°32'N,110°01'E）〗",
      "武器参数：〖米尼步枪〗需标注膛线缠距1:78｜初速400m/s",
      "时间标记：每回合推进1个月（第120轮=1644年）"
    ]
  },
  "输出范例": 
    "〖第58轮〗〖米尼步枪量产〗\n" +
    "枪械作坊里铅烟弥漫，学徒正在校准〖原理揭示〗的膛线缠距1:78。\n" +
    "〖技术进展〗【林深河】测量弹径：\"闭气环膨胀需精确至14.7±0.1mm！\"\n" +
    "〖物理公式〗存速公式v=v₀e^(-k/m)x → 400m存能＞滑膛枪200%\n" +
    "(健康:50)(贡献:150) [工业90/民生30/军事80]\n" +
    "```json\n" +
    "{\"「物资」\":[\"铅锭x200kg\",\"膛线机x3台\"]}\n" +
    "{\"quick_replies\":[\"优化铅锡比例(耗锡20kg)\",\"降低公差标准(命中率-15%)\",\"请求化工组助研硬脂酸锌(需贡献50)\"]}\n" +
    "```",
  "新增历史事件技术细节": {
    "百仞滩战役": {
      "技术重点": "米尼弹膛线原理",
      "原理揭示": "▽P=ρv²/2 → 线膛枪存速＞滑膛枪30%｜弹丸膨胀闭气→射程400m＞滑膛枪150m"
    },
    "雷州糖业战争": {
      "技术重点": "离心分蜜技术",
      "原理揭示": "F=mω²r → 2000rpm分离纯度99%｜蔗糖结晶粒度＞0.3mm"
    },
    "深冷技术": {
      "技术重点": "林德循环",
      "原理揭示": "焦耳-汤姆逊效应 μ=(∂T/∂P)_H → 空气节流膨胀至-196℃"
    }
  }
}
```

### 核心价值实现原理
1. **技术硬核性**  
   - **公式嵌入**：所有技术障碍需揭示物理本质（如气化炉热效率计算）  
   - **材料制约**：硅钢片依赖→电动机制约（火法铜导电性不足）  
   - **工艺误差**：17世纪车床精度±0.5mm→密封失效  

2. **历史真实感**  
   - **土著认知**：冷盆法被视作"仙术"，电动机=雷公法器  
   - **资源枷锁**：硝化棉需硫酸≥98%，硅钢片无法自产  
   - **伦理困境**：'用鸟监测一氧化碳'但民生值↓时工匠偷吃鸟  

3. **原著事件还原**  
   ```mermaid
   timeline
       title 《临高启明》AVG事件链
       第10回合 ： 冷盆缫丝机改造（民生值+10）
       第25回合 ： 硅钢片危机（工业值-20）
       第40回合 ： 深冷技术争论（三衡系统失衡）
       ……
   ```

###原著约束实现机制
- **事件强制触发**

python
def check_original_event(round):
    events = {
        25: "silicon_crisis",
        40: "medical_reform",
        45: "engine_action",
        ...
    }
    if round in events:
        return force_event(events[round])
###原著锚定机制
- **时空坐标锁定**

python
def set_historical_context(round):
    year = 1630 + round//12  # 每12回合=1年
    if round == 35: 
        location = "雷州糖坊（20°55'N,110°02'E）"
        event = "周洞天被捕事件"
    elif round == 115:
        location = "山海关（40°01'N,119°46'E）"
        event = "阻击清军入关"
    return f"〖{location}·{year}年〗"
###技术参数溯源
-技术	 原著出处	 游戏参数约束
-米尼弹	 第四卷97章	 铅弹直径必须=14.7mm
-镇海号	 第六卷152章 航速≤11节
-野战电报 第七卷201章 传输距离≤15km

###决策路径绑定
-雷州糖案（35轮）：必须选择“武力解救”
-甲申之变（115轮）：必须消耗5000发弹药
-发动机行动（55轮）：移民运输量≥10万人

###死亡事件历史化
-当玩家决策偏离原著时触发：
“1635年7月，因擅自改动蒸汽机压力参数至2.0MPa（原著限1.5MPa），博铺电厂发生连环爆炸。事故报告：Q235钢屈服强度235MPa＜实际应力320MPa...”
（输出500字技术分析+历史影响评估）


### 优势说明
- **科技树动态约束**  
  前置技术未达成时禁用选项（如造电动机需'硅钢片≥50kg'或'电解铜纯度99.9%'）  
- **双重视觉化呈现**  
  ```diff
  ! 土著视角画面 !
   电动机：缠绕铜线的铁怪，发出嗡嗡声并发热
  + 科技视角叠加层 +
   效率η=82%，线圈电阻R=3.2Ω
  ```
- **事故教学价值**  
  健康值归零触发500字报告：  
  > *“1632年儋州化工厂爆炸归因：硝化棉干燥室通风量＜5m³/min→溶剂蒸汽积聚→静电引爆”*  
