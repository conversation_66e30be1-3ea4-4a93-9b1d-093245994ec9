#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON解析修复功能
专门测试包含中文标点的复杂JSON解析
"""

import sys
import json
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI


def test_problematic_json():
    """测试有问题的JSON格式"""
    print("=" * 60)
    print("测试有问题的JSON格式")
    print("=" * 60)
    
    # 从实际AI响应中提取的有问题的JSON
    problematic_json = '["林清雪", "正道/玉虚宫/内门弟子 筑基九层半步金丹","提示词：你的任务是扮演林清雪，你的门派是玉虚宫，你和玩家的关系是师姐弟，你要安慰张凡并鼓励他努力修炼，注意在任何情况下不能脱离你的人物设定，如果话题和游戏以及剧情无关的问题可以忽略。","故事情节：张凡在魔族袭击中重伤昏迷，林清雪奉师傅之命找到他并带他回去疗伤，张凡追问自己的身世，林清雪告知了他。","你对玩家的看法：张凡虽然资质平平，但心地善良，被同门欺负也不记仇，我很同情他，希望他能努力修炼，改变自己的命运。","剧情：张凡得知自己的身世后，有些失落，需要安慰和鼓励。","当前关键聊天剧情设计限制(随机填入5-10)回合数":"6回合"]'
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        print("1. 测试标准JSON解析...")
        try:
            data = json.loads(problematic_json)
            print(f"  ✓ 标准解析成功，元素数量: {len(data)}")
        except json.JSONDecodeError as e:
            print(f"  ✗ 标准解析失败: {e}")
        
        print("\n2. 测试手动数组解析...")
        parsed_data = gui._parse_array_manually(problematic_json)
        if parsed_data and len(parsed_data) == 7:
            print(f"  ✓ 手动解析成功，元素数量: {len(parsed_data)}")
            print(f"  NPC名字: {parsed_data[0]}")
            print(f"  NPC身份: {parsed_data[1]}")
            print(f"  回合限制: {parsed_data[6]}")
        else:
            print(f"  ✗ 手动解析失败，结果: {parsed_data}")
        
        print("\n3. 测试完整触发检测...")
        
        # 构造完整的AI响应
        full_response = f'''游戏剧情内容...

{{"触发关键聊天剧情":{problematic_json}}}

更多内容...'''
        
        # 模拟对话完成回调
        def mock_dialogue_complete(history):
            print(f"  ✓ 对话完成回调被调用")
        
        # 临时替换回调函数
        original_callback = gui._on_dialogue_complete
        gui._on_dialogue_complete = mock_dialogue_complete
        
        result = gui._check_dialogue_trigger(full_response)
        
        # 恢复原始回调
        gui._on_dialogue_complete = original_callback
        
        if result:
            print("  ✓ 完整触发检测成功")
        else:
            print("  ✗ 完整触发检测失败")
        
        return result
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def test_array_parsing_edge_cases():
    """测试数组解析的边界情况"""
    print("=" * 60)
    print("测试数组解析的边界情况")
    print("=" * 60)
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        test_cases = [
            {
                "name": "包含中文冒号的数组",
                "array": '["元素1", "元素2：包含冒号", "元素3", "元素4", "元素5", "元素6", "元素7"]',
                "expected": 7
            },
            {
                "name": "包含逗号的字符串",
                "array": '["元素1", "元素2，包含逗号", "元素3", "元素4", "元素5", "元素6", "元素7"]',
                "expected": 7
            },
            {
                "name": "包含引号的字符串",
                "array": '["元素1", "元素2\\"包含引号", "元素3", "元素4", "元素5", "元素6", "元素7"]',
                "expected": 7
            },
            {
                "name": "多行格式",
                "array": '''[
                    "元素1",
                    "元素2",
                    "元素3",
                    "元素4",
                    "元素5",
                    "元素6",
                    "元素7"
                ]''',
                "expected": 7
            },
            {
                "name": "元素数量不足",
                "array": '["元素1", "元素2"]',
                "expected": None
            }
        ]
        
        success_count = 0
        total_count = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试 {test_case['name']}...")
            result = gui._parse_array_manually(test_case['array'])
            
            if test_case['expected'] is None:
                if result is None:
                    print(f"  ✓ 正确拒绝无效数组")
                    success_count += 1
                else:
                    print(f"  ✗ 应该拒绝但接受了，结果: {len(result) if result else 'None'}")
            else:
                if result and len(result) == test_case['expected']:
                    print(f"  ✓ 解析成功，元素数量: {len(result)}")
                    success_count += 1
                else:
                    print(f"  ✗ 解析失败，期望 {test_case['expected']} 个元素，实际 {len(result) if result else 'None'}")
        
        print(f"\n数组解析边界测试: {success_count}/{total_count} 通过")
        return success_count == total_count
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def test_real_world_scenario():
    """测试真实世界场景"""
    print("=" * 60)
    print("测试真实世界场景")
    print("=" * 60)
    
    # 完整的真实AI响应
    real_response = '''〖穿越之始 2/5〗第2回〖玉虚宫山门〗依旧云雾缥缈，灵气四溢，远处山峰在云雾中若隐若现，宛如仙境。  
【林清雪】（正道/玉虚宫/内门弟子）筑基九层半步金丹  
见你追问身世，她微微蹙眉，眼中闪过一丝怜悯，轻声说道："张凡师弟，你本是我玉虚宫外门弟子，自幼父母双亡，被师傅收养入我玉虚宫。你资质平平，练气一层多年未突破，常被同门欺辱。"  
你听后，心中五味杂陈（原来我在这世界是个可怜的废材，不过既来之则安之，我定要在这修仙世界闯出一番名堂）。  
「内心活动」看来我在这世界的处境很不妙啊，得想办法提升实力，不然迟早被人踩在脚下。  
「系统提示」：「了解身世1/3」  

```json
{"触发关键聊天剧情":["林清雪", "正道/玉虚宫/内门弟子 筑基九层半步金丹","提示词：你的任务是扮演林清雪，你的门派是玉虚宫，你和玩家的关系是师姐弟，你要安慰张凡并鼓励他努力修炼，注意在任何情况下不能脱离你的人物设定，如果话题和游戏以及剧情无关的问题可以忽略。","故事情节：张凡在魔族袭击中重伤昏迷，林清雪奉师傅之命找到他并带他回去疗伤，张凡追问自己的身世，林清雪告知了他。","你对玩家的看法：张凡虽然资质平平，但心地善良，被同门欺负也不记仇，我很同情他，希望他能努力修炼，改变自己的命运。","剧情：张凡得知自己的身世后，有些失落，需要安慰和鼓励。","当前关键聊天剧情设计限制(随机填入5-10)回合数":"6回合"]}
```

```json
{"quick_replies":["询问如何提升实力", "询问门派修炼资源", "询问师傅的情况", "表示会努力修炼"]}
```'''
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = ChatGUI()
        gui.root.withdraw()
        
        print("测试真实AI响应的完整处理流程...")
        
        # 模拟对话完成回调
        def mock_dialogue_complete(history):
            print(f"  ✓ 对话完成回调被调用，历史长度: {len(history)}")
        
        # 临时替换回调函数
        original_callback = gui._on_dialogue_complete
        gui._on_dialogue_complete = mock_dialogue_complete
        
        # 测试触发检测
        result = gui._check_dialogue_trigger(real_response)
        
        # 恢复原始回调
        gui._on_dialogue_complete = original_callback
        
        if result:
            print("✓ 真实场景测试成功")
        else:
            print("✗ 真实场景测试失败")
        
        return result
        
    except Exception as e:
        print(f"✗ 真实场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()


def main():
    """主测试函数"""
    print("开始测试JSON解析修复功能")
    print("=" * 80)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("有问题的JSON格式", test_problematic_json()))
    test_results.append(("数组解析边界情况", test_array_parsing_edge_cases()))
    test_results.append(("真实世界场景", test_real_world_scenario()))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！JSON解析修复成功！")
        print("\n修复内容:")
        print("1. 添加了手动数组解析功能")
        print("2. 处理包含中文标点符号的JSON")
        print("3. 改进了JSON修复逻辑")
        print("4. 增强了错误恢复能力")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return 1


if __name__ == "__main__":
    sys.exit(main())
