#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试用户开通的火山引擎模型
尝试不同的模型名称格式
"""

import os
import sys
import json
import requests
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_environment():
    """加载环境变量"""
    try:
        from dotenv import load_dotenv
        env_path = project_root / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            return True
        else:
            print(f"⚠ 环境变量文件不存在: {env_path}")
            return False
    except ImportError:
        print("⚠ python-dotenv 未安装")
        return True

def test_model_variations():
    """测试用户开通模型的不同命名格式"""
    print("=" * 60)
    print("测试用户开通的火山引擎模型")
    print("=" * 60)
    
    # 加载环境变量
    load_environment()
    api_key = os.getenv('volcengine_API_KEY')
    
    if not api_key:
        print("✗ 未找到API密钥")
        return False
    
    print(f"✓ 已获取API密钥: {api_key[:10]}...")
    
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 用户开通的模型的各种可能命名格式
    model_variations = [
        # 原始格式（控制台显示的）
        "Doubao-Seed-1.6-flash",
        "Doubao-Seed-1.6-thinking", 
        "Doubao-Seed-1.6",
        
        # 小写格式
        "doubao-seed-1.6-flash",
        "doubao-seed-1.6-thinking",
        "doubao-seed-1.6",
        
        # 下划线格式
        "doubao_seed_1_6_flash",
        "doubao_seed_1_6_thinking",
        "doubao_seed_1_6",
        
        # 简化格式
        "seed-1.6-flash",
        "seed-1.6-thinking",
        "seed-1.6",
        
        # 其他可能格式
        "doubao-seed-1-6-flash",
        "doubao-seed-1-6-thinking",
        "doubao-seed-1-6",
        
        # 已知可用的模型
        "doubao-1-5-pro-32k-250115",
        "doubao-pro-4k",
        "doubao-lite-4k"
    ]
    
    successful_models = []
    failed_models = []
    
    print(f"\n开始测试 {len(model_variations)} 种模型名称格式...\n")
    
    for i, model_name in enumerate(model_variations, 1):
        print(f"[{i:2d}/{len(model_variations)}] 测试模型: {model_name}")
        
        test_data = {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello"}
            ],
            "model": model_name,
            "max_tokens": 50,
            "temperature": 0.7,
            "stream": False
        }
        
        try:
            response = requests.post(url, headers=headers, json=test_data, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    usage = result.get('usage', {})
                    
                    print(f"  ✅ 成功！")
                    print(f"     响应: {content[:50]}{'...' if len(content) > 50 else ''}")
                    print(f"     Token: 输入={usage.get('prompt_tokens', 0)}, 输出={usage.get('completion_tokens', 0)}")
                    
                    successful_models.append({
                        'name': model_name,
                        'response': content,
                        'usage': usage
                    })
                else:
                    print(f"  ❌ 响应格式异常")
                    failed_models.append({'name': model_name, 'error': '响应格式异常'})
            else:
                try:
                    error_info = response.json()
                    error_msg = error_info.get('error', {}).get('message', '未知错误')
                    print(f"  ❌ 失败 (状态码: {response.status_code})")
                    print(f"     错误: {error_msg[:80]}{'...' if len(error_msg) > 80 else ''}")
                    failed_models.append({'name': model_name, 'error': error_msg})
                except:
                    print(f"  ❌ 失败 (状态码: {response.status_code})")
                    failed_models.append({'name': model_name, 'error': f'HTTP {response.status_code}'})
                    
        except requests.exceptions.Timeout:
            print(f"  ❌ 请求超时")
            failed_models.append({'name': model_name, 'error': '请求超时'})
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接错误")
            failed_models.append({'name': model_name, 'error': '连接错误'})
        except Exception as e:
            print(f"  ❌ 异常: {str(e)}")
            failed_models.append({'name': model_name, 'error': str(e)})
        
        # 避免请求过于频繁
        time.sleep(0.5)
    
    # 显示测试结果总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"\n✅ 成功的模型 ({len(successful_models)} 个):")
    if successful_models:
        for model in successful_models:
            print(f"  - {model['name']}")
            print(f"    响应: {model['response'][:60]}{'...' if len(model['response']) > 60 else ''}")
            usage = model['usage']
            print(f"    Token使用: 输入={usage.get('prompt_tokens', 0)}, 输出={usage.get('completion_tokens', 0)}")
            print()
    else:
        print("  无")
    
    print(f"\n❌ 失败的模型 ({len(failed_models)} 个):")
    if failed_models:
        # 按错误类型分组显示
        error_groups = {}
        for model in failed_models:
            error = model['error']
            if 'not exist' in error.lower() or 'not found' in error.lower():
                error_type = "模型不存在或无访问权限"
            elif 'not activated' in error.lower():
                error_type = "模型未激活"
            elif 'timeout' in error.lower():
                error_type = "请求超时"
            else:
                error_type = "其他错误"
            
            if error_type not in error_groups:
                error_groups[error_type] = []
            error_groups[error_type].append(model['name'])
        
        for error_type, models in error_groups.items():
            print(f"  {error_type} ({len(models)} 个):")
            for model_name in models:
                print(f"    - {model_name}")
            print()
    else:
        print("  无")
    
    # 提供建议
    print("\n💡 建议:")
    if successful_models:
        print("1. 使用测试成功的模型进行开发")
        print("2. 将成功的模型名称集成到聊天客户端中")
    else:
        print("1. 检查火山引擎控制台中的模型激活状态")
        print("2. 确认模型名称格式是否正确")
        print("3. 联系火山引擎技术支持获取正确的模型名称")
    
    print("3. 参考火山引擎官方文档获取最新的模型列表")
    print("4. 考虑使用已知可用的模型如 doubao-1-5-pro-32k-250115")
    
    return len(successful_models) > 0

def main():
    """主函数"""
    try:
        success = test_model_variations()
        return success
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
