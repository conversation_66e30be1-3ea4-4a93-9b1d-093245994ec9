#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Token限制修复效果
验证deepseek-chat模型的max_tokens参数自动调整功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_token_validation():
    """测试Token验证功能"""
    print("=" * 60)
    print("1. 测试Token验证功能")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.api_client import DeepSeekAPIClient
        
        client = DeepSeekAPIClient()
        print("✅ API客户端创建成功")
        
        # 测试不同的max_tokens值
        test_cases = [
            {"model": "deepseek-chat", "max_tokens": 100, "expected": 100},
            {"model": "deepseek-chat", "max_tokens": 8192, "expected": 8192},
            {"model": "deepseek-chat", "max_tokens": 65536, "expected": 8192},  # 应该被调整
            {"model": "deepseek-reasoner", "max_tokens": 65536, "expected": 65536},
            {"model": "deepseek-reasoner", "max_tokens": 100000, "expected": 65536},  # 应该被调整
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            model = test_case["model"]
            max_tokens = test_case["max_tokens"]
            expected = test_case["expected"]
            
            validated = client._validate_max_tokens(model, max_tokens)
            
            print(f"{i}. 模型: {model}, 输入: {max_tokens}, 期望: {expected}, 实际: {validated}")
            
            if validated == expected:
                print("   ✅ 验证通过")
            else:
                print(f"   ❌ 验证失败，期望{expected}，实际{validated}")
        
        return True
        
    except Exception as e:
        print(f"❌ Token验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_client_token_limits():
    """测试统一客户端的Token限制"""
    print("\n" + "=" * 60)
    print("2. 测试统一客户端Token限制")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        
        client = UnifiedAPIClient()
        print("✅ 统一客户端创建成功")
        
        # 测试不同模型的推荐Token数量
        models = ["deepseek-chat", "deepseek-reasoner"]
        
        for model in models:
            recommended = client.get_recommended_max_tokens(model)
            max_limit = client.get_model_max_tokens(model)
            
            print(f"模型: {model}")
            print(f"  推荐Token: {recommended}")
            print(f"  最大限制: {max_limit}")
            
            # 测试安全Token值计算
            test_values = [100, 8192, 32768, 65536]
            for test_val in test_values:
                safe_val = client.get_safe_token_value(model, test_val)
                print(f"  输入{test_val} -> 安全值{safe_val}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 统一客户端Token限制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_api_calls():
    """测试实际API调用"""
    print("\n" + "=" * 60)
    print("3. 测试实际API调用")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.api_client import DeepSeekAPIClient, ChatMessage
        
        client = DeepSeekAPIClient()
        print("✅ API客户端创建成功")
        
        messages = [ChatMessage(role="user", content="你好，请简单介绍一下你自己")]
        
        # 测试deepseek-chat模型，使用会被调整的大Token值
        print("\n测试deepseek-chat模型（大Token值自动调整）...")
        
        try:
            response_content = ""
            chunk_count = 0
            
            for chunk in client.chat_completion(
                messages=messages,
                model="deepseek-chat",
                max_tokens=65536,  # 这个值应该被自动调整为8192
                temperature=0.7,
                stream=True
            ):
                chunk_count += 1
                
                if chunk.error:
                    print(f"❌ API调用错误: {chunk.error}")
                    return False
                
                if chunk.is_complete:
                    break
                
                if chunk.content:
                    response_content += chunk.content
                    
                # 只处理前10个chunk
                if chunk_count > 10:
                    break
            
            print(f"✅ deepseek-chat模型测试成功")
            print(f"  处理了 {chunk_count} 个数据块")
            print(f"  响应内容: {response_content[:50]}...")
            
        except Exception as e:
            print(f"❌ deepseek-chat模型测试失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 实际API调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_model_switching():
    """测试GUI模型切换时的Token调整"""
    print("\n" + "=" * 60)
    print("4. 测试GUI模型切换Token调整")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        
        client = UnifiedAPIClient()
        print("✅ 统一客户端创建成功")
        
        # 模拟GUI中的模型切换场景
        scenarios = [
            {
                "name": "从deepseek-reasoner切换到deepseek-chat",
                "from_model": "deepseek-reasoner",
                "to_model": "deepseek-chat",
                "current_tokens": 65536
            },
            {
                "name": "从deepseek-chat切换到deepseek-reasoner",
                "from_model": "deepseek-chat", 
                "to_model": "deepseek-reasoner",
                "current_tokens": 8192
            }
        ]
        
        for scenario in scenarios:
            print(f"\n场景: {scenario['name']}")
            print(f"  当前Token设置: {scenario['current_tokens']}")
            
            # 获取目标模型的推荐Token
            recommended = client.get_recommended_max_tokens(scenario['to_model'])
            safe_tokens = client.get_safe_token_value(scenario['to_model'], scenario['current_tokens'])
            
            print(f"  切换到{scenario['to_model']}:")
            print(f"    推荐Token: {recommended}")
            print(f"    安全Token: {safe_tokens}")
            
            if safe_tokens <= recommended:
                print("    ✅ Token调整正确")
            else:
                print("    ❌ Token调整可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI模型切换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Token限制修复验证工具")
    print("验证deepseek-chat模型的max_tokens参数自动调整功能")
    
    # 运行所有测试
    tests = [
        test_token_validation,
        test_unified_client_token_limits,
        test_actual_api_calls,
        test_gui_model_switching
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试执行失败: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("修复验证总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"测试通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过，Token限制修复成功")
        print("\n修复效果:")
        print("1. deepseek-chat模型max_tokens自动限制在8192以内")
        print("2. deepseek-reasoner模型max_tokens支持65536")
        print("3. 超出限制的值会自动调整到安全范围")
        print("4. GUI模型切换时会自动调整Token设置")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
