#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API 测试脚本
用于验证API连接和请求格式
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.api_client import DeepSeekAPIClient, ChatMessage

def test_api_connection():
    """测试API连接"""
    print("=" * 50)
    print("DeepSeek API 连接测试")
    print("=" * 50)
    
    try:
        # 创建API客户端
        client = DeepSeekAPIClient()
        print("✓ API客户端创建成功")
        
        # 测试连接
        success, message = client.test_connection()
        if success:
            print(f"✓ {message}")
        else:
            print(f"✗ {message}")
            return False
        
        # 测试简单对话
        print("\n测试简单对话...")
        messages = [
            ChatMessage(role="user", content="你好，请简单介绍一下你自己。")
        ]
        
        print("发送消息:", messages[0].content)
        
        response_content = ""
        thinking_content = ""
        
        for chunk in client.chat_completion(
            messages=messages,
            model="deepseek-reasoner",
            max_tokens=100,
            stream=True
        ):
            if chunk.error:
                print(f"✗ API错误: {chunk.error}")
                return False
            
            if chunk.is_complete:
                break
            
            if chunk.content:
                if chunk.is_thinking:
                    thinking_content += chunk.content
                    print(f"[思考] {chunk.content}", end="", flush=True)
                else:
                    response_content += chunk.content
                    print(chunk.content, end="", flush=True)
        
        print("\n")
        print("=" * 50)
        print("✓ API测试完成")
        
        if thinking_content:
            print(f"思考过程长度: {len(thinking_content)} 字符")
        
        if response_content:
            print(f"回复内容长度: {len(response_content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"✗ API测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_api_connection()
    sys.exit(0 if success else 1)
