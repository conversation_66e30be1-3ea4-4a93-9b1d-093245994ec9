#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户体验优化手动演示
让用户实际体验三个关键优化功能
"""

import sys
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI


def create_demo_gui():
    """创建演示GUI"""
    print("创建用户体验优化演示GUI...")
    
    # 创建主窗口
    app = ChatGUI()
    
    # 准备演示用的AI响应（包含触发对话的JSON）
    demo_response = '''
〖修仙之路 1/5〗第1回〖青云山门〗青云山坐落在云雾缭绕的山峰之上，山门巍峨壮观。

【林清雪】（正道/玉虚宫/内门弟子）筑基九层半步金丹
见你刚刚到达山门，她温和地看着你，轻声说道："师弟，你看起来有些疲惫，是刚从远方赶来的吗？"

你点了点头（确实经过了长途跋涉才到达这里）。

「内心活动」这位师姐看起来很和善，也许可以向她了解一些情况。

```json
{"触发关键聊天剧情":["林清雪", "正道/玉虚宫/内门弟子 筑基九层半步金丹","你是林清雪，玉虚宫的内门弟子，修为筑基九层半步金丹。你性格温和善良，经常帮助新来的师弟师妹适应宗门生活。","你与玩家是同门关系，你比玩家入门较早，对宗门情况很了解。你看到玩家刚到山门，想要主动关心和帮助他。","你性格温和，乐于助人，说话温柔有耐心。你对修炼很有心得，也很关心同门的成长。","当前在青云山门前，你刚好路过看到了新来的玩家，准备主动上前关心询问。","当前关键聊天剧情设计限制(随机填入5-10)回合数":"4回合"]}
```

```json
{"quick_replies":["询问宗门情况", "请教修炼问题", "感谢师姐关心", "表示需要休息"]}
```
    '''
    
    # 添加演示按钮和说明
    demo_frame = tk.Frame(app.main_frame)
    demo_frame.pack(fill=tk.X, pady=10)
    
    # 标题
    title_label = tk.Label(
        demo_frame,
        text="🎮 用户体验优化演示",
        font=("Microsoft YaHei", 14, "bold"),
        fg="#2c3e50"
    )
    title_label.pack(pady=5)
    
    # 说明文字
    info_text = """
📋 本演示将展示三个关键优化功能：
1. 🤖 对话窗口自动开场 - NPC主动开始对话
2. 🔚 最后一轮自然结束 - 对话自然收尾
3. 🔄 主线剧情衔接 - 对话结束后自动继续主线

点击下方按钮开始体验！
    """
    
    info_label = tk.Label(
        demo_frame,
        text=info_text,
        font=("Microsoft YaHei", 10),
        fg="#34495e",
        justify=tk.LEFT
    )
    info_label.pack(pady=5)
    
    # 演示按钮
    def start_demo():
        """开始演示"""
        print("🎬 开始用户体验优化演示...")
        
        # 模拟接收到AI响应
        app._display_message("assistant", demo_response)
        app.history_manager.add_message("assistant", demo_response)
        
        # 解析并显示（这会触发对话窗口）
        app._parse_and_show_quick_replies(demo_response)
        
        print("✨ 请观察以下优化效果：")
        print("1. 对话窗口打开后，林清雪会自动开场（不需要等待玩家输入）")
        print("2. 进行几轮对话后，最后一轮林清雪会自然地结束对话")
        print("3. 对话窗口关闭后，主聊天会自动继续剧情发展")
    
    demo_button = tk.Button(
        demo_frame,
        text="🚀 开始体验优化演示",
        command=start_demo,
        bg="#3498db",
        fg="white",
        font=("Microsoft YaHei", 12, "bold"),
        padx=20,
        pady=10
    )
    demo_button.pack(pady=10)
    
    # 使用说明
    usage_frame = tk.Frame(app.main_frame)
    usage_frame.pack(fill=tk.X, pady=5)
    
    usage_text = """
💡 使用说明：
• 点击演示按钮后，会弹出对话窗口
• 观察林清雪是否主动开场（优化1）
• 与她进行几轮对话，体验自然的对话流程
• 注意最后一轮她会自然地结束对话（优化2）
• 对话结束后，观察主聊天是否自动继续剧情（优化3）
    """
    
    usage_label = tk.Label(
        usage_frame,
        text=usage_text,
        font=("Microsoft YaHei", 9),
        fg="#7f8c8d",
        justify=tk.LEFT
    )
    usage_label.pack(pady=5)
    
    print("演示GUI创建完成")
    print("=" * 60)
    print("🎯 用户体验优化功能演示")
    print("=" * 60)
    print("本演示将展示以下三个关键优化：")
    print()
    print("1. 🤖 对话窗口自动开场")
    print("   - 问题：之前对话窗口打开后显示空白，用户不知所措")
    print("   - 解决：现在NPC会主动开场，引导玩家进入对话")
    print()
    print("2. 🔚 最后一轮对话自然结束")
    print("   - 问题：之前对话达到回合限制时直接结束，很突兀")
    print("   - 解决：现在最后一轮会自然收尾，引导回到主线")
    print()
    print("3. 🔄 对话结束后主线剧情衔接")
    print("   - 问题：之前对话结束后直接返回主聊天，缺乏连续性")
    print("   - 解决：现在会自动触发主线剧情继续，保持故事流畅")
    print()
    print("请在GUI中点击演示按钮开始体验！")
    print("=" * 60)
    
    return app


def main():
    """主函数"""
    print("🎮 启动用户体验优化演示程序")
    print("=" * 80)
    
    try:
        # 创建演示GUI
        app = create_demo_gui()
        
        # 运行GUI
        app.run()
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断演示")
        return 0
    except Exception as e:
        print(f"💥 演示程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("演示完成")
    return 0


if __name__ == "__main__":
    sys.exit(main())
