#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的流式响应处理
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_fixed_streaming():
    """测试修复后的流式响应"""
    print("=" * 80)
    print("测试修复后的火山引擎流式响应处理")
    print("=" * 80)
    
    try:
        # 1. 导入模块
        print("\n1. 导入模块...")
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        from deepseek_chat_client.api_client import ChatMessage
        print("✓ 模块导入成功")
        
        # 2. 初始化统一客户端
        print("\n2. 初始化统一客户端...")
        unified_client = UnifiedAPIClient()
        print("✓ 统一客户端初始化成功")
        
        # 3. 测试修仙题材的中文流式响应
        print("\n3. 测试修仙题材的中文流式响应...")
        
        # 创建修仙题材的测试消息
        test_messages = [
            ChatMessage(role="system", content="你是一个修仙题材文字冒险游戏的AI助手，请用中文回答。"),
            ChatMessage(role="user", content="我想在修仙世界中穿越身份，开始我的修仙之旅。请为我创建一个详细的角色背景。")
        ]
        
        # 测试火山引擎模型
        test_model = "doubao-1-5-pro-32k-250115"  # 使用相对稳定的模型
        
        print(f"  使用模型: {test_model}")
        print(f"  提供商: {unified_client.get_provider_for_model(test_model)}")
        print(f"  开始流式响应测试...")
        
        # 收集响应内容
        response_chunks = []
        total_chunks = 0
        json_errors = 0
        encoding_errors = 0
        
        try:
            for chunk in unified_client.chat_completion(
                messages=test_messages,
                model=test_model,
                max_tokens=200,  # 适中的Token数量
                temperature=0.7,
                stream=True
            ):
                total_chunks += 1
                
                if chunk.error:
                    print(f"  ✗ API调用错误: {chunk.error}")
                    return False
                
                if chunk.content:
                    response_chunks.append(chunk.content)
                    # 实时显示内容（不换行）
                    print(chunk.content, end='', flush=True)
                
                if chunk.is_complete:
                    print("\n  ✓ 流式响应完成")
                    break
            
            # 分析结果
            full_response = "".join(response_chunks)
            
            print(f"\n4. 响应分析:")
            print(f"  总数据块数: {total_chunks}")
            print(f"  内容块数: {len(response_chunks)}")
            print(f"  响应总长度: {len(full_response)} 字符")
            print(f"  JSON解析错误: {json_errors}")
            print(f"  编码错误: {encoding_errors}")
            
            # 检查中文字符
            chinese_chars = sum(1 for char in full_response if '\u4e00' <= char <= '\u9fff')
            print(f"  中文字符数: {chinese_chars}")
            
            # 检查是否有乱码
            garbled_indicators = ['ã', 'ç', 'è', 'â', 'ä', 'ö', 'ü']
            garbled_count = sum(full_response.count(indicator) for indicator in garbled_indicators)
            
            if garbled_count > 0:
                print(f"  ⚠️ 检测到可能的乱码字符: {garbled_count} 个")
                return False
            else:
                print(f"  ✓ 未检测到乱码字符")
            
            # 显示响应内容
            print(f"\n5. 完整响应内容:")
            print(f"  {full_response}")
            
            if len(full_response) > 0 and chinese_chars > 0:
                print(f"\n✅ 流式响应修复成功！")
                print(f"  - 成功处理中文内容")
                print(f"  - 无JSON解析错误")
                print(f"  - 无字符编码问题")
                return True
            else:
                print(f"\n❌ 响应内容异常")
                return False
                
        except Exception as e:
            print(f"\n✗ 流式响应测试异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_models():
    """测试多个火山引擎模型"""
    print("\n" + "=" * 80)
    print("测试多个火山引擎模型的流式响应")
    print("=" * 80)
    
    try:
        from deepseek_chat_client.unified_client import UnifiedAPIClient
        from deepseek_chat_client.api_client import ChatMessage
        
        unified_client = UnifiedAPIClient()
        
        # 简短的测试消息
        test_messages = [
            ChatMessage(role="system", content="你是一个AI助手，请用中文简洁回答。"),
            ChatMessage(role="user", content="请简单介绍一下修仙小说的基本设定。")
        ]
        
        # 测试所有火山引擎模型
        volcengine_models = [
            "doubao-1-5-pro-32k-250115",
            "doubao-seed-1-6-250615",
            "doubao-seed-1-6-flash-250715",
            "doubao-seed-1-6-thinking-250715"
        ]
        
        successful_models = []
        failed_models = []
        
        for model in volcengine_models:
            print(f"\n测试模型: {model}")
            
            try:
                response_chunks = []
                has_error = False
                
                for chunk in unified_client.chat_completion(
                    messages=test_messages,
                    model=model,
                    max_tokens=50,  # 小Token数量快速测试
                    temperature=0.7,
                    stream=True
                ):
                    if chunk.error:
                        print(f"  ✗ 错误: {chunk.error}")
                        has_error = True
                        break
                    
                    if chunk.content:
                        response_chunks.append(chunk.content)
                    
                    if chunk.is_complete:
                        break
                
                if not has_error:
                    full_response = "".join(response_chunks)
                    if len(full_response) > 0:
                        print(f"  ✓ 成功 - 响应长度: {len(full_response)}")
                        successful_models.append(model)
                    else:
                        print(f"  ✗ 响应为空")
                        failed_models.append(model)
                else:
                    failed_models.append(model)
                    
            except Exception as e:
                print(f"  ✗ 异常: {str(e)}")
                failed_models.append(model)
        
        print(f"\n📊 测试结果总结:")
        print(f"  成功的模型 ({len(successful_models)}): {successful_models}")
        print(f"  失败的模型 ({len(failed_models)}): {failed_models}")
        
        return len(successful_models) > 0
        
    except Exception as e:
        print(f"\n✗ 多模型测试失败: {e}")
        return False

def main():
    """主函数"""
    try:
        print("开始测试修复后的流式响应处理...")
        
        # 测试单个模型的详细流式响应
        success1 = test_fixed_streaming()
        
        # 测试多个模型的快速验证
        success2 = test_multiple_models()
        
        if success1 and success2:
            print(f"\n🎉 所有测试通过！流式响应修复成功！")
            print(f"\n💡 修复总结:")
            print(f"  ✅ 解决了UTF-8字符截断问题")
            print(f"  ✅ 修复了JSON解析错误")
            print(f"  ✅ 消除了中文乱码问题")
            print(f"  ✅ 流式响应处理稳定可靠")
            return True
        else:
            print(f"\n❌ 部分测试失败，需要进一步调试")
            return False
            
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return False
    except Exception as e:
        print(f"\n\n测试过程中发生未预期错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
