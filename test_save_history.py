#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试保存历史功能的脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from deepseek_chat_client.gui import ChatGUI
from deepseek_chat_client.config import config
import tkinter as tk

def test_save_history():
    """测试保存历史功能"""
    print("=" * 50)
    print("测试保存历史功能")
    print("=" * 50)
    
    try:
        # 创建GUI实例
        gui = ChatGUI()
        
        # 模拟一些聊天内容
        test_content = """=== 测试聊天记录 ===

[2025-07-18 22:35:12] 玩家:
新游戏开始

[2025-07-18 22:35:15] 游戏:
第1天（早上）

意识边疆的黎明时分，天空中弥漫着淡淡的电磁雾霾。你站在塔克拉玛干"女娲城"（人类）的观察台上，远眺着这个被三大阵营撕裂的世界。

今天是2077年7月3日，距离AI阵营宣布的"硅基生命进化"最后通牒还有97天。整个世界都在屏息等待着什么...

你是一名隶属于人类阵营的特工，代号"守望者"。刚刚收到来自"认知安全联合委员会"（人类）的紧急任务简报。

```json
["接受任务", "了解更多", "查看装备", "联系总部"]
```

[2025-07-18 22:35:20] 玩家:
接受任务

[2025-07-18 22:35:25] 游戏:
你毫不犹豫地接受了任务。通讯器中传来指挥官冷静而坚定的声音：

"守望者，情况紧急。我们的间谍卫星发现，AI阵营在北极冰盖下的反光子计算中心出现异常活动。根据《燧人氏》生物计算机（人类）的分析，他们可能正在加速'种子计划'的进程。"

"你的任务是潜入缓冲地带，与我们的内线'白鸽'接头，获取更多情报。记住，这次行动关系到人类文明的存亡。"

就在这时，一个身影悄然出现在你身后...

```json
["转身查看", "保持警戒", "继续听取", "准备武器"]
```
"""
        
        # 将测试内容插入到消息显示区域
        gui.message_text.config(state=tk.NORMAL)
        gui.message_text.insert(tk.END, test_content)
        gui.message_text.config(state=tk.DISABLED)
        
        print("✓ 已插入测试聊天内容")
        
        # 测试保存历史功能
        print("测试保存历史功能...")
        gui._save_history_to_file()
        
        print("✓ 保存历史功能测试完成")
        
        # 检查UserBook文件夹是否创建
        userbook_dir = config.project_root / "UserBook"
        if userbook_dir.exists():
            print(f"✓ UserBook文件夹已创建: {userbook_dir}")
            
            # 列出文件夹中的文件
            files = list(userbook_dir.glob("*.txt"))
            if files:
                print(f"✓ 找到 {len(files)} 个历史文件:")
                for file in files:
                    print(f"  - {file.name} ({file.stat().st_size} 字节)")
                    
                # 读取最新文件的内容
                latest_file = max(files, key=lambda f: f.stat().st_mtime)
                print(f"\n最新文件内容预览 ({latest_file.name}):")
                print("-" * 30)
                with open(latest_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content[:500] + "..." if len(content) > 500 else content)
            else:
                print("✗ UserBook文件夹中没有找到文件")
        else:
            print("✗ UserBook文件夹未创建")
        
        # 关闭GUI
        gui.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_save_history()
    sys.exit(0 if success else 1)
