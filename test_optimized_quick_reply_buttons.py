#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的快捷回复按钮显示效果
验证动态宽度调整、文本截断、工具提示等功能
"""

import sys
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_text_processing_methods():
    """测试文本处理方法"""
    print("=" * 60)
    print("1. 测试文本处理方法")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏主窗口
        
        # 测试文本宽度计算
        test_texts = [
            "Hello",
            "你好",
            "Hello世界",
            "询问师姐修炼心得",
            "向师姐请教关于筑基期突破的具体方法和注意事项，包括需要准备的丹药、功法要求等",
            "English text with some 中文字符 mixed together",
            "A very long English text that should be truncated properly"
        ]
        
        print("文本宽度计算测试:")
        for text in test_texts:
            width = gui._calculate_text_width(text)
            print(f"  '{text}' -> 宽度: {width}")
        
        print("\n文本截断测试:")
        for text in test_texts:
            truncated = gui._truncate_text(text, 25)
            print(f"  原文: '{text}'")
            print(f"  截断: '{truncated}'")
            print()
        
        print("按钮宽度计算测试:")
        for text in test_texts:
            truncated = gui._truncate_text(text, 25)
            width_px = gui._calculate_button_width_px(truncated)
            print(f"  '{truncated}' -> 按钮宽度: {width_px}px")
        
        gui.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 文本处理方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tooltip_functionality():
    """测试工具提示功能"""
    print("\n" + "=" * 60)
    print("2. 测试工具提示功能")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.gui import ToolTip
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("工具提示测试")
        root.geometry("400x200")
        
        # 创建测试按钮
        test_texts = [
            "短文本",
            "这是一个比较长的文本，应该会显示工具提示",
            "向师姐请教关于筑基期突破的具体方法和注意事项，包括需要准备的丹药、功法要求等详细信息"
        ]
        
        for i, text in enumerate(test_texts):
            btn = tk.Button(root, text=text[:15] + "..." if len(text) > 15 else text)
            btn.pack(pady=10)
            
            # 添加工具提示
            tooltip = ToolTip(btn, text)
            print(f"✅ 为按钮 {i+1} 添加工具提示: '{text}'")
        
        print("\n请在测试窗口中将鼠标悬停在按钮上查看工具提示效果")
        print("关闭窗口继续测试...")
        
        # 运行测试窗口
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 工具提示功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_button_layout_optimization():
    """测试按钮布局优化"""
    print("\n" + "=" * 60)
    print("3. 测试按钮布局优化")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.title("快捷回复按钮布局测试")
        
        # 测试不同长度的快捷回复选项
        test_scenarios = [
            {
                "name": "短文本选项",
                "options": ["是", "否", "好的", "谢谢", "再见"]
            },
            {
                "name": "中等长度选项",
                "options": ["询问师姐修炼心得", "请教炼丹技巧", "了解门派规矩", "查看任务进度"]
            },
            {
                "name": "长文本选项",
                "options": [
                    "向师姐请教关于筑基期突破的具体方法和注意事项",
                    "询问关于炼制筑基丹所需的珍贵药材获取途径",
                    "了解门派内部的各种修炼功法和心法的详细介绍",
                    "请教如何在修炼过程中避免走火入魔的风险"
                ]
            },
            {
                "name": "混合长度选项",
                "options": [
                    "是",
                    "询问师姐修炼心得",
                    "向师姐请教关于筑基期突破的具体方法和注意事项，包括需要准备的丹药",
                    "好的",
                    "了解更多详细信息",
                    "谢谢师姐的指导"
                ]
            }
        ]
        
        current_scenario = 0
        
        def show_next_scenario():
            nonlocal current_scenario
            if current_scenario < len(test_scenarios):
                scenario = test_scenarios[current_scenario]
                print(f"\n显示测试场景: {scenario['name']}")
                print(f"选项: {scenario['options']}")
                
                # 显示快捷回复按钮
                gui._show_quick_reply_buttons(scenario['options'])
                
                current_scenario += 1
                
                # 更新状态栏
                gui.status_var.set(f"测试场景 {current_scenario}/{len(test_scenarios)}: {scenario['name']}")
            else:
                gui.status_var.set("所有测试场景完成")
                print("\n✅ 所有测试场景完成")
        
        # 添加控制按钮
        control_frame = tk.Frame(gui.root)
        control_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
        
        next_btn = tk.Button(
            control_frame,
            text="下一个测试场景",
            command=show_next_scenario,
            font=("Arial", 10)
        )
        next_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(
            control_frame,
            text="清除按钮",
            command=gui._hide_quick_reply_buttons,
            font=("Arial", 10)
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加说明标签
        info_label = tk.Label(
            control_frame,
            text="点击'下一个测试场景'查看不同长度文本的按钮显示效果",
            font=("Arial", 9),
            fg="#666666"
        )
        info_label.pack(side=tk.LEFT, padx=20)
        
        # 显示第一个场景
        show_next_scenario()
        
        print("\n请在GUI窗口中测试以下功能:")
        print("1. 观察不同长度文本的按钮显示效果")
        print("2. 检查文本截断和省略号显示")
        print("3. 测试工具提示功能（鼠标悬停在长文本按钮上）")
        print("4. 观察按钮的自动换行效果")
        print("5. 测试按钮的点击功能")
        
        # 运行GUI
        gui.run()
        
        return True
        
    except Exception as e:
        print(f"❌ 按钮布局优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("4. 测试边界情况")
    print("=" * 60)
    
    try:
        from deepseek_chat_client.gui import ChatGUI
        
        # 创建GUI实例
        gui = ChatGUI()
        gui.root.withdraw()  # 隐藏主窗口
        
        # 测试边界情况
        edge_cases = [
            "",  # 空字符串
            "a",  # 单字符
            "中",  # 单个中文字符
            "a" * 100,  # 超长英文
            "中" * 50,  # 超长中文
            "🎮🎯📖❤️🎮",  # 表情符号
            "   空格测试   ",  # 包含空格
            "\n换行\n测试\n",  # 包含换行符
            "特殊字符!@#$%^&*()",  # 特殊字符
        ]
        
        print("边界情况测试:")
        for i, text in enumerate(edge_cases):
            print(f"\n{i+1}. 测试文本: '{repr(text)}'")
            
            try:
                # 测试文本宽度计算
                width = gui._calculate_text_width(text)
                print(f"   文本宽度: {width}")
                
                # 测试文本截断
                truncated = gui._truncate_text(text, 25)
                print(f"   截断结果: '{truncated}'")
                
                # 测试按钮宽度计算
                button_width = gui._calculate_button_width_px(truncated)
                print(f"   按钮宽度: {button_width}px")
                
                print(f"   ✅ 边界情况 {i+1} 处理正常")
                
            except Exception as e:
                print(f"   ❌ 边界情况 {i+1} 处理失败: {e}")
        
        gui.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("优化后的快捷回复按钮显示效果测试")
    print("验证动态宽度调整、文本截断、工具提示等功能")
    
    # 运行所有测试
    tests = [
        test_text_processing_methods,
        test_edge_cases
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试执行失败: {e}")
            results.append(False)
    
    # 询问是否运行交互式测试
    try:
        response = input("\n是否运行交互式测试？(y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            # 运行工具提示测试
            tooltip_result = test_tooltip_functionality()
            results.append(tooltip_result)
            
            # 运行按钮布局测试
            layout_result = test_button_layout_optimization()
            results.append(layout_result)
    except KeyboardInterrupt:
        print("\n交互式测试已取消")
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"测试通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过，快捷回复按钮优化成功")
        print("\n优化功能:")
        print("1. ✅ 动态按钮宽度调整")
        print("2. ✅ 智能文本截断和省略号")
        print("3. ✅ 工具提示显示完整内容")
        print("4. ✅ 自动换行布局")
        print("5. ✅ 边界情况处理")
    else:
        print("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
